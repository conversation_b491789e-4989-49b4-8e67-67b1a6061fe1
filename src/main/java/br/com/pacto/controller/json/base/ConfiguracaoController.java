package br.com.pacto.controller.json.base;


import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.configuracoes.ConfiguracaoDobras;
import br.com.pacto.controller.json.SuperController;
import br.com.pacto.controller.json.gogood.ConfigGoGoodDTO;
import br.com.pacto.controller.json.gympass.ConfigGymPassDTO;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.json.mgb.ConfigMgb;
import br.com.pacto.controller.json.mqv.MqvDTO;
import br.com.pacto.controller.json.selfloops.SelfloopsDTO;
import br.com.pacto.controller.json.totalpass.TotalPassDTO;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.gogood.ConfigGoGoodService;
import br.com.pacto.service.intf.gympass.ConfigGymPassService;
import br.com.pacto.service.intf.log.LogService;
import br.com.pacto.service.intf.selfloops.SelfloopsConfiguracoesService;
import br.com.pacto.service.intf.usuario.UsuarioService;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.bean.GenericoTO;
import br.com.pacto.util.impl.UtilReflection;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import servicos.integracao.zw.IntegracaoCadastrosWSConsumer;

import javax.faces.model.SelectItem;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.pacto.objeto.Uteis.incluirLog;
import static java.util.Arrays.asList;


@Controller
@RequestMapping("/psec/configuracoes")
public class ConfiguracaoController extends SuperController {

    private ConfiguracaoSistemaService configuracaoSistemaService;

    private transient IntegracaoCadastrosWSConsumer integracaoWS = (IntegracaoCadastrosWSConsumer) UtilContext.getBean(IntegracaoCadastrosWSConsumer.class);

    @Autowired
    private SessaoService sessaoService;

    @Autowired
    private UsuarioService usuarioService;

    @Autowired
    private HttpServletRequest httpServletRequest;

    @Autowired
    private LogDao logDao;

    @Autowired
    private LogService logService;

    @Autowired
    private SelfloopsConfiguracoesService selfloopsConfiguracoesService;

    @Autowired
    public ConfiguracaoController(ConfiguracaoSistemaService configuracaoSistemaService) {
        Assert.notNull(configuracaoSistemaService, "O serviço de configuração não foi injetado corretamente");
        this.configuracaoSistemaService = configuracaoSistemaService;
    }

    private ResponseEntity<EnvelopeRespostaDTO> send(Class classe, Object obj) {
        try {
            Object objantes = UtilReflection.copy(obj);
            Object objAntesAlteracao = configuracaoSistemaService.configsDTO(classe, objantes);
            configuracaoSistemaService.gravarCfgsDTO(null, classe, obj);
            try {
                configuracaoSistemaService.purgeCache(sessaoService.getUsuarioAtual().getChave());
                configuracaoSistemaService.notificarOuvintes(sessaoService.getUsuarioAtual().getChave(), httpServletRequest);
            } catch (Exception e) {
                e.printStackTrace();
            }
            incluirLog(sessaoService.getUsuarioAtual().getChave(), classe.getSimpleName().toUpperCase(), null, configuracaoSistemaService.getDescricaoParaLog(objAntesAlteracao, obj),
                    configuracaoSistemaService.getDescricaoParaLog(obj, objAntesAlteracao),
                    "ALTERAÇÃO",
                    "ALTERAÇÃO DE CONFIGURAÇÃO "+classe.getSimpleName().toUpperCase(),
                    EntidadeLogEnum.CONFIGURACAOSISTEMA, "Configuração Sistema", sessaoService, logDao);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao alterar a configuração", e);
            switch (e.getCodigoError()){
                case 400:
                    return ResponseEntityFactory.erroConhecido(e.getChaveExcecao(), e.getMessage());
                default:
                    return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/gerais", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerais(@RequestBody ConfiguracoesGeraisDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/gerais/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atividadesCrossfit(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO,
                    "'"+ConfiguracoesGeraisDTO.class.getSimpleName().toUpperCase()+"'",
                    "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/integracoes/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> integracoesLog(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA,
                    filtros, paginadorDTO, "'"+ConfiguracaoIntegracoesGravarEnumDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aplicativos", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerias(@RequestBody ConfiguracoesAplicativosDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/aplicativos/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logApps(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA,
                    filtros, paginadorDTO, "'"+ConfiguracoesAplicativosDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/treino", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerias(@RequestBody ConfiguracoesTreinoDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/treino/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logTreino(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                            PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'"+ConfiguracoesTreinoDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/crossfit", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgCrossfit(@RequestBody ConfiguracoesCrossfitDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/aulas", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAulas(@RequestBody ConfiguracoesAulasDTO cfgDTO) {
        cfgDTO.setMinutos_agendar_com_antecedencia(Uteis.removerTudoMenosNumero(cfgDTO.getMinutos_agendar_com_antecedencia()));
        cfgDTO.setMinutos_desmarcar_com_antecedencia(Uteis.removerTudoMenosNumero(cfgDTO.getMinutos_desmarcar_com_antecedencia()));
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/aulas/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logAulas(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                       PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'"+ConfiguracoesAulasDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAvaliacoes(@RequestBody ConfiguracoesAvaliacaoDTO cfgDTO) {
        String key = sessaoService.getUsuarioAtual().getChave();

        if(cfgDTO.getCfg_dobras_cutaneas()){
            Gson gson = new Gson();
            List<ConfiguracaoDobras> cfgsDobras = gson.fromJson(cfgDTO.getOrdens_dobras(), new TypeToken<ArrayList<ConfiguracaoDobras>>(){}.getType());
            try {
                configuracaoSistemaService.gravarCfgsDobras(key, cfgsDobras);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logAvaliacao(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'"+ConfiguracoesAvaliacaoDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/gestao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGestao(@RequestBody ConfiguracoesGestaoDTO cfgDTO){
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/gestao/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logGestao(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                  PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'"+ConfiguracoesGestaoDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/integracoes", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoes(@RequestBody ConfiguracaoIntegracoesDTO cfgDTOIntegracao,
                                                              @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        ConfiguracaoIntegracoesGravarEnumDTO cfgDTO = new ConfiguracaoIntegracoesGravarEnumDTO();
        cfgDTO = cfgDTO.constructor(cfgDTOIntegracao);
        ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        ConfigGymPassDTO dto = new ConfigGymPassDTO();
        dto.setEmpresa(empresaId);
        popularConfiguracoesGympass(dto, cfgDTOIntegracao, ctx, empresaId);
        if (dto.getEmpresa() != null) {
            configService.alterarDTO(ctx, dto);
        }
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/integracoeslista", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesLista(@RequestBody List<ConfigGymPassDTO> cfgDTOIntegracaoLista,
                                                              @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        for(ConfigGymPassDTO dto : cfgDTOIntegracaoLista){
            ConfiguracaoIntegracoesDTO cfgDTOIntegracao = new ConfiguracaoIntegracoesDTO();
            cfgDTOIntegracao = cfgDTOIntegracao.constructor(dto);
            popularConfiguracoesGympass(dto, cfgDTOIntegracao, ctx, empresaId);
            configService.alterarDTO(ctx, dto);
        }

        return ResponseEntityFactory.ok("OK");
    }

    @ResponseBody
    @RequestMapping(value = "/integracoeslistaGoGood", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesListaGoGood(@RequestBody List<ConfigGoGoodDTO> cfgDTOIntegracaoLista,
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        ConfigGoGoodService configService = UtilContext.getBean(ConfigGoGoodService.class);
        String ctx = sessaoService.getUsuarioAtual().getChave();
        for(ConfigGoGoodDTO dto : cfgDTOIntegracaoLista){
            ConfiguracaoIntegracoesDTO cfgDTOIntegracao = new ConfiguracaoIntegracoesDTO();
            cfgDTOIntegracao = cfgDTOIntegracao.constructor(dto);
            popularConfiguracoesGoGood(dto, cfgDTOIntegracao, ctx, empresaId);
            configService.alterarDTO(ctx, dto);
        }

        return ResponseEntityFactory.ok("OK");
    }

    @ResponseBody
    @RequestMapping(value = "/integracoeslistamgb", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesListaMGB(@RequestBody List<ConfigMgb> configMGB,
                                                                   @RequestHeader(value = "empresaId", required = true) Integer empresaId) throws Exception {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            configuracaoSistemaService.inserirConfigsMGB(key, configMGB);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/manutencao/aulasexperimentais", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pcrAtualizarNumeroAulasExperimentaisTodos(@RequestParam("nraulasexperimentais") Integer nraulasexperimentais) {
        try {
            configuracaoSistemaService.atualizarManutencao(nraulasexperimentais);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao alterar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }

    }

    @ResponseBody
    @RequestMapping(value = "/manutencao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgManutencao() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesManutencaoDTO.class, new ConfiguracoesManutencaoDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/manutencao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGeriasManutencao(@RequestBody ConfiguracoesManutencaoDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/gerais", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerais() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesGeraisDTO.class, new ConfiguracoesGeraisDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aulas", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAulas() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesAulasDTO.class, new ConfiguracoesAulasDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/aplicativos", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAplicativos() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesAplicativosDTO.class, new ConfiguracoesAplicativosDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/treino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgTreino() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesTreinoDTO.class, new ConfiguracoesTreinoDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/crossfit", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgCrossfit() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesCrossfitDTO.class, new ConfiguracoesCrossfitDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgAvaliacao() {
        List<SelectItem> produtos;
        Gson gson = new Gson();
        try {
            String key = sessaoService.getUsuarioAtual().getChave();
            produtos = new ArrayList<SelectItem>();

            if(!SuperControle.independente(key)){
                List<GenericoTO> genericoTOS = integracaoWS.produtosServicosZW(Aplicacao.getProp(key, Aplicacao.urlZillyonWebInteg), key);
                produtos.add(new SelectItem(0, ""));
                for(GenericoTO g : genericoTOS){
                    produtos.add(new SelectItem(g.getCodigo(), g.getLabel()));
                }
            }
            ConfiguracoesAvaliacaoDTO configuracoesAvaliacaoDTO = (ConfiguracoesAvaliacaoDTO)configuracaoSistemaService.configsDTO(ConfiguracoesAvaliacaoDTO.class, new ConfiguracoesAvaliacaoDTO());
            configuracoesAvaliacaoDTO.setProdutos(gson.toJson(produtos));
            List<ConfiguracaoDobras> dobras = configuracaoSistemaService.obterCfgsDobras(key);
            configuracoesAvaliacaoDTO.setOrdens_dobras(gson.toJson(dobras));
            return ResponseEntityFactory.ok(configuracoesAvaliacaoDTO);

        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntityFactory.erroInterno(e.getMessage(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/gestao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGestao(){
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesGestaoDTO.class, new ConfiguracoesGestaoDTO()));
        } catch (ServiceException e){
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/integracoes", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoes(@RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            ConfiguracaoIntegracoesDTO configs = (ConfiguracaoIntegracoesDTO) configuracaoSistemaService.configsDTO(ConfiguracaoIntegracoesDTO.class, new ConfiguracaoIntegracoesDTO());
            String ctx = sessaoService.getUsuarioAtual().getChave();
            configs = configuracaoSistemaService.obterConfigsBookingGympass(ctx, empresaId, configs);
            return ResponseEntityFactory.ok(configs);
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/integracoeslista", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesLista(@RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            ConfiguracaoIntegracoesDTO configs = (ConfiguracaoIntegracoesDTO) configuracaoSistemaService.configsDTO(ConfiguracaoIntegracoesDTO.class, new ConfiguracaoIntegracoesDTO());
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfigGymPassService configService = UtilContext.getBean(ConfigGymPassService.class);
            List<ConfigGymPassDTO> listaConfigGymPass = configService.obterTodosDTO(ctx);
            return ResponseEntityFactory.ok(listaConfigGymPass);
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    private void popularConfiguracoesGympass(ConfigGymPassDTO dto, ConfiguracaoIntegracoesDTO cfgDTOIntegracao, String ctx, Integer empresaId) throws Exception {
        dto.setCodigoGymPass(cfgDTOIntegracao.getCodigo_gympass_booking());
        dto.setUsarGymPassBooking(cfgDTOIntegracao.getUsar_gympass_booking());
        dto.setEmpresa(dto.getEmpresa());
    }

    private void popularConfiguracoesGoGood(ConfigGoGoodDTO dto, ConfiguracaoIntegracoesDTO cfgDTOIntegracao, String ctx, Integer empresaId) throws Exception {
        dto.setTokenAcademyGoGood(cfgDTOIntegracao.getToken_academy_gogood());
        dto.setEmpresa(dto.getEmpresa());
    }

    @ResponseBody
    @RequestMapping(value = "/processos/excluirNaoExisteZw/{empresa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pcrExcluirNaoExisteZw(@PathVariable("empresa") Integer empresa) {
        String key = sessaoService.getUsuarioAtual().getChave();
            try {
                configuracaoSistemaService.executarExclusaoCliNaoExisteZw(key, empresa);
                return ResponseEntityFactory.ok("OK");
            } catch (ServiceException e) {
                Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
                return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
            }
    }

    @ResponseBody
    @RequestMapping(value = "/integracoeslistamgb", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIntegracoesListaMGB(@RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.obterConfigsMGB(key));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/sincronizarBooking/{empresa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarBooking(@PathVariable("empresa") Integer empresa) {
        String key = sessaoService.getUsuarioAtual().getChave();
            try {
                configuracaoSistemaService.sincronizarBooking(key, empresa);
                return ResponseEntityFactory.ok("OK");
            } catch (Exception e) {
                Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
                return ResponseEntityFactory.erroInterno("erro_sincronizar", e.getMessage());
            }
    }

    @ResponseBody
    @RequestMapping(value = "/notificacao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgNotificacao(){
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesNotificacaoDTO.class, new ConfiguracoesNotificacaoDTO()));
        } catch (ServiceException e){
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/notificacao", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgNotificacao(@RequestBody ConfiguracoesNotificacaoDTO cfgDTO){
        try {
            configuracaoSistemaService.gravarCfgsNotificacaoDTO(null, cfgDTO.getClass(), cfgDTO);
            return ResponseEntityFactory.ok("Salvo com sucesso!");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao alterar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-mqv", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesMQV() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(configuracaoSistemaService.obterIntegracoesMQV(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-selfloops", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesSelfloops() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(selfloopsConfiguracoesService.obterIntegracoesSelfloops(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/salvar-integracoes-mqv", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesMQV(@RequestBody List<MqvDTO> mqvDTOList) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            configuracaoSistemaService.salvarIntegracoesMQV(ctx, mqvDTOList);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/salvar-integracoes-selfloops", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesSelfloops(@RequestBody List<SelfloopsDTO> selfloopsDTOList) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            selfloopsConfiguracoesService.salvarIntegracoesSelfloops(ctx, selfloopsDTOList);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/processos/sincronizar-alunos-mgb/{empresa}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> pcrSincronizarAlunosMgb(@PathVariable("empresa") Integer empresa) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.sincronizarAlunosMgb(key, empresa));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/set-aluno-parq-false/{matricula}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> setAlunoParqFalse(@PathVariable("matricula") Integer matricula) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            String retorno = configuracaoSistemaService.setAlunoParqFalse(key, matricula);
            return ResponseEntityFactory.ok(retorno);
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getCause().toString(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/corrigir-datas-matriculaalunohorarioturma", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> corrigirDatasMatriculaalunohorarioturma(@RequestParam("codigoMatricula") Integer codigoMatricula,
                                                                                       @RequestParam("dataInicio") String dataInicio,
                                                                                       @RequestParam("dataFim") String dataFim) {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        try {
            String retorno = "";
            if (ctx != null) {
                Map<String, String> params = new HashMap<>();
                params.put("chave", ctx);
                params.put("codMAHT", codigoMatricula.toString());
                params.put("dataInicio", dataInicio);
                params.put("dataFim", dataFim);
                String url = Aplicacao.getProp(ctx, Aplicacao.urlZillyonWeb);
                String urlParams = ExecuteRequestHttpService.obterUrlComParams(url + "/prest/manutencao/corrigir-datas-matriculaalunohorarioturma", params,"UTF-8");

                String retornoZw = ExecuteRequestHttpService.executeRequestGET(urlParams);
                if (new JSONObject(retornoZw).has("content")) {
                    if (new JSONObject(retornoZw).getString("content").equals("ok")) {
                        retorno = "Datas da matricula do aluno atualizadas com sucesso!";
                    } else {
                        retorno = new JSONObject(retornoZw).getString("content");
                    }
                } else {
                    retorno = "Ocorreu um problema ao tentar atualizar as datas da matricula do aluno!";
                }
            } else {
                retorno = "Chave não encontrada!";
            }

            return ResponseEntityFactory.ok(retorno);
        } catch (Exception e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getCause().toString(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/sync-professortw/{empresaZW}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarProfessorTW(@PathVariable("empresaZW") Integer empresaZW) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.sincronizarProfessoresTW(key, empresaZW));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-totalpass", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesTotalPass() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(configuracaoSistemaService.obterIntegracoesTotalPass(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/obter-integracoes-gogood", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> obterIntegracoesGoGood(@RequestHeader(value = "empresaId", required = true) Integer empresaId) {
        try {
            ConfiguracaoIntegracoesDTO configs = (ConfiguracaoIntegracoesDTO) configuracaoSistemaService.configsDTO(ConfiguracaoIntegracoesDTO.class, new ConfiguracaoIntegracoesDTO());
            String ctx = sessaoService.getUsuarioAtual().getChave();
            ConfigGoGoodDTO dto = configuracaoSistemaService.obterConfigsGoGood(ctx, empresaId, configs);
            return ResponseEntityFactory.ok(asList(dto));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/salvar-integracoes-totalpass", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> salvarIntegracoesTotalPass(@RequestBody List<TotalPassDTO> totalPassDTOList) {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            configuracaoSistemaService.salvarIntegracoesTotalPass(ctx, totalPassDTOList);
            return ResponseEntityFactory.ok("OK");
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/usa-plano-compartilhado", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> verificaPermiteDependente() {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            return ResponseEntityFactory.ok(configuracaoSistemaService.verificaPermiteDependente(ctx));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            return ResponseEntityFactory.erroInterno(e.getCause().toString(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/ia", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgGerias(@RequestBody ConfiguracoesIaDTO cfgDTO) {
        return send(cfgDTO.getClass(), cfgDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/ia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cfgIa() {
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.configsDTO(ConfiguracoesIaDTO.class, new ConfiguracoesIaDTO()));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao consultar a configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/ia/log", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> logIa(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                         PaginadorDTO paginadorDTO) throws JSONException {
        try {
            return ResponseEntityFactory.ok(logService.listarLog(EntidadeLogEnum.CONFIGURACAOSISTEMA, filtros, paginadorDTO, "'"+ConfiguracoesIaDTO.class.getSimpleName().toUpperCase()+"'", "", ""));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao buscar log de configuração", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/processos/sync-atividades-professorestw/{empresaZW}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> sincronizarAtividadesProfessoresTW(@PathVariable("empresaZW") Integer empresaZW) {
        String key = sessaoService.getUsuarioAtual().getChave();
        try {
            return ResponseEntityFactory.ok(configuracaoSistemaService.sincronizarAtividadesProfessoresTW(key, empresaZW));
        } catch (ServiceException e) {
            Logger.getLogger(ConfiguracaoController.class.getName()).log(Level.SEVERE, "Erro ao executar o processo", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

}
