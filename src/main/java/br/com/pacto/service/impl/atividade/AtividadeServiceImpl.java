/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.pacto.service.impl.atividade;

import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.impl.EntityManagerService;
import br.com.pacto.base.jpa.service.impl.PovoadorAtividadeServiceImpl;
import br.com.pacto.base.jpa.service.intf.PovoadorAtividadeService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.animacao.TipoAnimacaoEnum;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.aparelho.AparelhoAjuste;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAlternativa;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeCategoriaAtividade;
import br.com.pacto.bean.atividade.AtividadeCompletaResponseTO;
import br.com.pacto.bean.atividade.AtividadeEmpresa;
import br.com.pacto.bean.atividade.AtividadeEmpresaResponseTO;
import br.com.pacto.bean.atividade.AtividadeEmpresaTO;
import br.com.pacto.bean.atividade.AtividadeFicha;
import br.com.pacto.bean.atividade.AtividadeFichaAjuste;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeImagemResponseTO;
import br.com.pacto.bean.atividade.AtividadeImagemTO;
import br.com.pacto.bean.atividade.AtividadeImagemUploadTO;
import br.com.pacto.bean.atividade.AtividadeMusculo;
import br.com.pacto.bean.atividade.AtividadeNivel;
import br.com.pacto.bean.atividade.AtividadeRelacionadaResponseTO;
import br.com.pacto.bean.atividade.AtividadeSimplesResponseTO;
import br.com.pacto.bean.atividade.AtividadeTO;
import br.com.pacto.bean.atividade.AtividadeVideo;
import br.com.pacto.bean.atividade.AtividadeVideoTO;
import br.com.pacto.bean.atividade.CadastrosAuxiliaresEnum;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.MetodoExecucaoEnum;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.atividade.TipoAtividadeImagemEnum;
import br.com.pacto.bean.configuracoes.ConfiguracaoSistema;
import br.com.pacto.bean.configuracoes.ConfiguracoesEnum;
import br.com.pacto.bean.empresa.Empresa;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.musculo.GrupoMuscularResponseTO;
import br.com.pacto.bean.musculo.Musculo;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.bean.serie.Serie;
import br.com.pacto.bean.usuario.Usuario;
import br.com.pacto.controller.json.atividade.FiltroAtividadeCrossfitJSON;
import br.com.pacto.controller.json.atividade.FiltroAtividadeJSON;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.base.SuperControle;
import br.com.pacto.controller.json.log.EntidadeLogEnum;
import br.com.pacto.controller.to.UploadedFile;
import br.com.pacto.dao.intf.animacao.AnimacaoDao;
import br.com.pacto.dao.intf.aparelho.AparelhoDao;
import br.com.pacto.dao.intf.atividade.AtividadeAlternativaDao;
import br.com.pacto.dao.intf.atividade.AtividadeAnimacaoDao;
import br.com.pacto.dao.intf.atividade.AtividadeAparelhoDao;
import br.com.pacto.dao.intf.atividade.AtividadeCategoriaAtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeDao;
import br.com.pacto.dao.intf.atividade.AtividadeFichaAjusteDao;
import br.com.pacto.dao.intf.atividade.AtividadeFichaDao;
import br.com.pacto.dao.intf.atividade.AtividadeGrupoMuscularDao;
import br.com.pacto.dao.intf.atividade.AtividadeMusculoDao;
import br.com.pacto.dao.intf.atividade.AtividadeNivelDao;
import br.com.pacto.dao.intf.atividade.AtividadeVideoDao;
import br.com.pacto.dao.intf.atividade.CategoriaAtividadeDao;
import br.com.pacto.dao.intf.atividadeEmpresa.AtividadeEmpresaDao;
import br.com.pacto.dao.intf.empresa.EmpresaDao;
import br.com.pacto.dao.intf.log.LogDao;
import br.com.pacto.dao.intf.musculo.GrupoMuscularDao;
import br.com.pacto.dao.intf.musculo.MusculoDao;
import br.com.pacto.dao.intf.nivel.NivelDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.security.service.SessaoService;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.exception.ValidacaoException;
import br.com.pacto.service.impl.avaliacao.ResizeImage;
import br.com.pacto.service.impl.musculo.GrupoMuscularServiceImpl;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeExcecoes;
import br.com.pacto.service.impl.notificacao.excecao.AtividadeFichaExcecoes;
import br.com.pacto.service.intf.Validacao;
import br.com.pacto.service.intf.animacao.AnimacaoService;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.atividadeEmpresa.AtividadeEmpresaService;
import br.com.pacto.service.intf.configuracoes.ConfiguracaoSistemaService;
import br.com.pacto.service.intf.empresa.EmpresaService;
import br.com.pacto.util.GifSequenceWriter;
import br.com.pacto.util.UteisValidacao;
import br.com.pacto.util.UtilContext;
import br.com.pacto.util.ViewUtils;
import br.com.pacto.util.enumeradores.MidiasNiveisEnum;
import br.com.pacto.util.impl.Ordenacao;
import br.com.pacto.util.impl.UtilReflection;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.hibernate.Hibernate;
import org.hibernate.exception.ConstraintViolationException;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import servicos.integracao.admapp.client.Midia;
import servicos.integracao.midias.MidiaService;
import servicos.integracao.midias.commons.MidiaEntidadeEnum;
import br.com.pacto.service.impl.atividade.RelatorioExclusaoAtividadeIADTO;
import br.com.pacto.service.impl.atividade.AtividadeNaoExcluidaInfoDTO;

import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.Normalizer;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

import static br.com.pacto.bean.atividade.AtividadeFicha.getAtividadeAnimacaoDao;
import static br.com.pacto.bean.atividade.AtividadeFicha.getAtividadeEmpresaService;
import static br.com.pacto.objeto.Uteis.incluirLog;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 *
 * <AUTHOR>
 */
@Service
public class AtividadeServiceImpl implements AtividadeService {

    @Autowired
    private ViewUtils viewUtils;
    @Autowired
    private Validacao validacao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AnimacaoService animacaoService;
    @Autowired
    private AparelhoDao aparelhoDao;
    @Autowired
    private MusculoDao musculoDao;
    @Autowired
    private GrupoMuscularDao grupoMuscularDao;
    @Autowired
    private AnimacaoDao animacaoDao;
    @Autowired
    private CategoriaAtividadeDao categoriaAtividadeDao;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    @Autowired
    private AtividadeAnimacaoDao atvAnimacaoDao;
    @Autowired
    private AtividadeFichaAjusteDao atividadeFichaAjusteDao;
    @Autowired
    private SessaoService sessaoService;
    @Autowired
    private EmpresaDao empresaDao;
    @Autowired
    private AtividadeEmpresaDao atividadeEmpresaDao;
    @Autowired
    private NivelDao nivelDao;
    @Autowired
    private AtividadeCategoriaAtividadeDao atividadeCategoriaAtividadeDao;
    @Autowired
    private AtividadeAparelhoDao atividadeAparelhoDao;
    @Autowired
    private AtividadeGrupoMuscularDao atividadeGrupoMuscularDao;
    @Autowired
    private AtividadeMusculoDao atividadeMusculoDao;
    @Autowired
    private AtividadeNivelDao atividadeNivelDao;
    @Autowired
    private AtividadeAlternativaDao atividadeAlternativaDao;
    @Autowired
    private EmpresaService empresaService;
    @Autowired
    private LogDao logDao;
    @Autowired
    private GrupoMuscularServiceImpl grupoMuscularService;
    @Autowired
    private PovoadorAtividadeService povoadorAtividadeService;
    @Autowired
    private AtividadeVideoDao atividadeVideoDao;
    @Autowired
    private AtividadeAnimacaoDao atividadeAnimacaoDao;
    @Autowired
    private ConfiguracaoSistemaService configuracaoSistemaService;

    private ConcurrentHashMap<String, List<AtividadeJSON>> mapTodasAtividades = new ConcurrentHashMap<>();

    private ConcurrentHashMap<String, List<AtividadeJSON>> mapTodasAtividadesCross = new ConcurrentHashMap<>();

    public ViewUtils getViewUtils() {
        return viewUtils;
    }

    public Validacao getValidacao() {
        return validacao;
    }

    public AtividadeDao getAtividadeDao() {
        return atividadeDao;
    }

    public AparelhoDao getAparelhoDao() {
        return aparelhoDao;
    }

    public MusculoDao getMusculoDao() {
        return musculoDao;
    }

    public GrupoMuscularDao getGrupoMuscularDao() {
        return grupoMuscularDao;
    }

    public AnimacaoDao getAnimacaoDao() {
        return animacaoDao;
    }

    public CategoriaAtividadeDao getCategoriaAtividadeDao() {
        return categoriaAtividadeDao;
    }

    @Override
    public Atividade add(final String ctx, Atividade atividade, Midia midia) throws ServiceException {
        try {

            Animacao animacao = new Animacao(atividade.getNome(), atividade.getDescricao(), midia.getNome(), TipoAnimacaoEnum.IMAGEM);
            if (getAnimacaoDao().exists(ctx, animacao, "titulo")) {
                animacao = getAnimacaoDao().findListByAttributes(ctx,
                        new String[]{"titulo"}, new Object[]{atividade.getNome().trim()},
                        "titulo", 0).get(0);
            } else {
                animacao = getAnimacaoDao().insert(ctx, animacao);
            }
            atividade.getAnimacoes().add(new AtividadeAnimacao(animacao, atividade));
            List<Atividade> atividades = getAtividadeDao().findListByAttributes(ctx,
                    new String[]{"nome"}, new Object[]{atividade.getNome()},
                    "nome", 0);
            if (atividades.isEmpty()) {
                atividade = getAtividadeDao().insert(ctx, atividade);
            } else {
                atividade = atividades.get(0);
            }

            return atividade;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Atividade add(final String ctx, TipoAtividadeEnum tipo, final String nome,
                         String[] categorias, final String[] urls, final String[] aparelhos, final String[] grupos) throws ServiceException {
        Atividade atv = new Atividade();
        atv.setAtivo(true);
        atv.setTipo(tipo);
        atv.setNome(nome);
        try {
            if (categorias != null) {
                for (int i = 0; i < categorias.length; i++) {
                    String categ = categorias[i].trim();
                    if (!categ.isEmpty()) {
                        atv.getCategorias().add(
                                new AtividadeCategoriaAtividade(
                                        getCategoriaAtividadeDao().insertOrGetObjectForName(ctx, categ), atv));
                    }
                }
            }
            //
            if (urls != null) {
                for (int i = 0; i < urls.length; i++) {
                    String url = urls[i].trim();
                    if (!url.isEmpty()) {
                        Animacao anim = new Animacao(atv.getNome(), atv.getDescricao(), url, TipoAnimacaoEnum.IMAGEM);
                        atv.getAnimacoes().add(new AtividadeAnimacao(getAnimacaoDao().insertOrGetObjectForName(ctx, anim, "titulo"), atv));
                    }
                }
            }
            //
            if (aparelhos != null) {
                for (int i = 0; i < aparelhos.length; i++) {
                    String aparelho = aparelhos[i].trim();
                    if (!aparelho.isEmpty()) {
                        Aparelho ap = new Aparelho(aparelho);
                        atv.getAparelhos().add(new AtividadeAparelho(getAparelhoDao().insertOrGetObjectForName(ctx, ap, "nome"), atv));
                    }
                }
            }
            if (grupos != null) {
                for (int i = 0; i < grupos.length; i++) {
                    String grupo = grupos[i].trim();
                    if (!grupo.isEmpty()) {
                        GrupoMuscular g = new GrupoMuscular(grupo);
                        atv.getGruposMusculares().add(new AtividadeGrupoMuscular(getGrupoMuscularDao().insertOrGetObjectForName(ctx, g, "nome"), atv));
                    }
                }
            }
            return inserir(ctx, atv);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Aparelho addAparelho(final String ctx, Atividade atividade, final String nome, TipoAtividadeEnum tipo) throws ServiceException {
        Aparelho aparelho = new Aparelho();
        aparelho.setNome(nome);
        try {
            atividade.getAparelhos().add(
                    new AtividadeAparelho(
                            getAparelhoDao().insertOrGetObjectForName(ctx, nome), atividade));
            return aparelho;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public boolean remove(Atividade atividade, final AtividadeAparelho aparelho) {
        return atividade.getAparelhos().remove(aparelho);
    }

    @Override
    public void addMusculos(final String ctx, Atividade atividade, final String[] nomes) throws ServiceException {
        for (int i = 0; i < nomes.length; i++) {
            String musculo = nomes[i];
            try {
                atividade.getMusculos().add(
                        new AtividadeMusculo(
                                getMusculoDao().insertOrGetObjectForName(ctx, musculo), atividade));
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        }
    }

    @Override
    public boolean remove(Atividade atividade, final AtividadeMusculo musculo) {
        return atividade.getMusculos().remove(musculo);
    }

    @Override
    public void addGruposMusculares(final String ctx, Atividade atividade, final String[] nomes) throws ServiceException {
        for (int i = 0; i < nomes.length; i++) {
            String grupo = nomes[i];
            try {
                atividade.getGruposMusculares().add(
                        new AtividadeGrupoMuscular(
                                getGrupoMuscularDao().insertOrGetObjectForName(ctx, grupo), atividade));
            } catch (Exception ex) {
                throw new ServiceException(ex);
            }
        }
    }

    @Override
    public boolean remove(Atividade atividade, AtividadeGrupoMuscular grupoMuscular) {
        return atividade.getGruposMusculares().remove(grupoMuscular);
    }

    @Override
    public Animacao addAnimacao(final String ctx, Atividade atividade, final String titulo, final String subtitulo, final String url,
                                TipoAnimacaoEnum tipo) throws ServiceException {
        try {
            Animacao animacao = new Animacao(titulo, subtitulo, url, tipo);
            atividade.getAnimacoes().add(
                    new AtividadeAnimacao(
                            getAnimacaoDao().insertOrGetObjectForName(ctx, animacao, "titulo"), atividade));
            return animacao;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public boolean remove(Atividade atividade, AtividadeAnimacao atividadeAnimacao) {
        return atividade.getAnimacoes().remove(atividadeAnimacao);
    }

    @Override
    public Atividade alterar(final String ctx, Atividade object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
            Atividade atividade = getAtividadeDao().update(ctx, object);
            return atividade;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void apenasValidar(final String ctx, Atividade object) throws ServiceException, ValidacaoException {
        try {
            validarDados(ctx, object);
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void excluir(final String ctx, Atividade object) throws ServiceException {
        try {
            getAtividadeDao().delete(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Atividade inserir(final String ctx, Atividade object) throws ServiceException {
        try {
            validarDados(ctx, object);
            Atividade atividade = getAtividadeDao().insert(ctx, object);
            return atividade;
        } catch (ValidacaoException ex) {
            throw ex;
        } catch (Exception ex) {
            Throwable cause = ex.getCause();
            if (cause instanceof ConstraintViolationException) {
                SQLException sqlEx = ((ConstraintViolationException) cause).getSQLException();
                String sqlMessage = sqlEx.getMessage();
                throw new ServiceException(sqlMessage);
            }
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public Atividade obterObjetoPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAtividadeDao().findObjectByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public Atividade obterPorId(final String ctx, Integer id) throws ServiceException {
        try {
            return getAtividadeDao().findById(ctx, id);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Atividade> obterPorParam(final String ctx, String query, Map<String, Object> params)
            throws ServiceException {
        try {
            return getAtividadeDao().findByParam(ctx, query, params);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Atividade> obterPorParam(final String ctx, String query,
                                         Map<String, Object> params, int max, int index)
            throws ServiceException {
        try {
            return getAtividadeDao().findByParam(ctx, query, params, max, index);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Atividade> obterTodos(final String ctx, boolean somenteAtivas, boolean somenteInativas, boolean crossfit) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder("SELECT obj FROM Atividade obj WHERE obj.nome IS NOT NULL AND TRIM(obj.nome) NOT LIKE '' ");
            if (somenteAtivas && !somenteInativas) {
                sql.append(" AND obj.ativo IS TRUE");
            }
            if (somenteInativas && !somenteAtivas) {
                sql.append(" AND obj.ativo IS NOT TRUE");
            }
            sql.append(" AND obj.crossfit = ").append(crossfit).append(" ");
            sql.append(" ORDER by obj.nome ");
            return getAtividadeDao().findByParam(ctx, sql.toString(), new HashMap<String, Object>());
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    public void atualizar(final String ctx, Atividade object) throws ServiceException {
        try {
            getAtividadeDao().refresh(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    @Override
    public List<Atividade> obterPorRelacionamento(final String ctx, Integer codigo, String relacionamento, String atributo) throws ServiceException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM Atividade obj ");
        sql.append(" INNER JOIN obj.").append(relacionamento).append(" atr ");
        Map<String, Object> params = new HashMap<String, Object>();
        if (codigo != null && codigo > 0) {
            sql.append(" with atr.").append(atributo).append(".codigo = :_codigo");
            params.put("_codigo", codigo);
        }
        try {
            return obterPorParam(ctx, sql.toString(), params);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void removerRelacoes(String ctx, Atividade object) throws ServiceException {
        try {
            object.getAparelhos().clear();
            object.getMusculos().clear();
            object.getGruposMusculares().clear();
            object.getAnimacoes().clear();
            object.getFichas().clear();
            object.getCategorias().clear();
            object.getNiveis().clear();
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void validarDados(final String ctx, Atividade object) throws ValidacaoException {
        //Inicializar objetos antes de salvar.
        Hibernate.initialize(object.getEmpresasHabilitadas());
        Hibernate.initialize(object.getCategorias());
        Hibernate.initialize(object.getAparelhos());
        Hibernate.initialize(object.getGruposMusculares());
        Hibernate.initialize(object.getMusculos());
        Hibernate.initialize(object.getNiveis());

        if ((object.getNome() == null || object.getNome().isEmpty()) && object.getTipo() == null && !object.getCrossfit()) {
            throw new ValidacaoException(getViewUtils().getMensagem("validacao.nome.tipo"));
        }
        if (object.getNome() == null || object.getNome().isEmpty()) {
            throw new ValidacaoException(getViewUtils().getMensagem("validacao.nome"));
        }
        if (object.getTipo() == null && !object.getCrossfit()) {
            throw new ValidacaoException(getViewUtils().getMensagem("validacao.tipo"));
        }
        boolean atividadeIA = object.getIdIA2() != null && object.getIdIA2() > 0;
        if (getAtividadeDao().exists(ctx, object, "nome") && !atividadeIA) {
            throw new ValidacaoException(getViewUtils().getMensagem("cadastros.atividade.existente"));
        }
        object.setTodasEmpresas(object.getEmpresasHabilitadas() == null || object.getEmpresasHabilitadas().isEmpty());
    }

    @Override
    public List<Atividade> obterPorRelacionamento(final String ctx, Map<CadastrosAuxiliaresEnum, List<Integer>> params, String nome,
                                                  final Usuario usuario) throws ServiceException {
        return obterPorRelacionamento(ctx, params, nome, usuario, null, null);
    }
    public List<Atividade> obterPorRelacionamento(final String ctx, Map<CadastrosAuxiliaresEnum, List<Integer>> params, String nome,
                                                  final Usuario usuario, Integer empresa, PaginadorDTO paginadorDTO) throws ServiceException {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT obj FROM Atividade obj ");
        sql.append(" WHERE ativo is true ");
        sql.append(" AND crossfit is false ");
        sql.append(" AND TRIM(obj.nome) NOT LIKE '' ");

        if(usuario != null || empresa != null){
            sql.append(" and (todasEmpresas is true or ");
            sql.append(" obj.codigo IN (SELECT atividade.codigo from AtividadeEmpresa atve WHERE atve.empresa.codZW = ");
            sql.append(empresa == null ? usuario.getEmpresaZW() : empresa).append("))");
        }
        for (CadastrosAuxiliaresEnum atr : params.keySet()) {
            if(params.get(atr) == null || params.get(atr).isEmpty()){
                continue;
            }
            String codigos = "";
            for (Integer codigo : params.get(atr)) {
                codigos += "," + codigo;
            }
            sql.append(" AND obj.codigo IN (SELECT sub.atividade.codigo FROM ")
                    .append(atr.getNomeEntidade()).append(" sub WHERE sub.").append(atr.getNomeAtributo()).append(".codigo IN (").append(codigos.replaceFirst(",", "")).append("))");
        }
        if (nome != null && !nome.isEmpty()) {
            sql.append(params.isEmpty() ? " WHERE " : " AND ").append(" LOWER(obj.nome) LIKE  '").append(nome.toLowerCase()).append("%' ");
        }
        HashMap<String, Object> param = new HashMap<>();
        if(paginadorDTO != null){
            try {
                paginadorDTO.setQuantidadeTotalElementos(atividadeDao.countWithParamHqlFull(ctx,
                        sql.toString().replaceFirst("SELECT obj", "SELECT COUNT(obj.codigo) ")
                        , param).longValue());
            }catch (Exception e){
                Uteis.logar(e, AtividadeServiceImpl.class);
            }
        }

        sql.append(" ORDER BY obj.nome ");
        try {
            return paginadorDTO == null ? obterPorParam(ctx, sql.toString(), param) :
                    atividadeDao.findByParam(ctx, sql.toString(), param, paginadorDTO.getSize().intValue(), paginadorDTO.getPage().intValue());
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    @Override
    public void montarListaAtividadesFicha(String ctx, List<AtividadeFicha> atividadesFicha) {
        for (AtividadeFicha at : atividadesFicha) {
            Ordenacao.ordenarLista(at.getSeries(), "codigo");
        }
    }

    @Override
    public List<AtividadeFichaAjuste> consultarAjustes(String ctx, AtividadeFicha atividadeFicha) throws ServiceException {
        try {
            return atividadeFichaAjusteDao.findListByAttributes(ctx, new String[]{"atividadeFicha.codigo"},
                    new Object[]{atividadeFicha.getCodigo()}, null, 0);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }

    }

    @Override
    public List<AtividadeFichaAjuste> montarAjustes(String ctx, Atividade atividade, List<AtividadeFichaAjuste> ajustes) {
        ajustes = ajustes == null ? new ArrayList<AtividadeFichaAjuste>() : ajustes;
        Set<AtividadeFichaAjuste> ajustesMontado = new HashSet<AtividadeFichaAjuste>();
        for (AtividadeAparelho atvAp : atividade.getAparelhos()) {
            for (AparelhoAjuste apAj : atvAp.getAparelho().getAjustes()) {
                AtividadeFichaAjuste atfa = new AtividadeFichaAjuste(null, apAj.getNome(), "");
                int index = ajustes.indexOf(atfa);
                atfa = index < 0 ? atfa : ajustes.get(index);
                ajustesMontado.add(atfa);
            }
        }
        List<AtividadeFichaAjuste> lista = new ArrayList<AtividadeFichaAjuste>();
        lista.addAll(ajustesMontado);
        return lista;
    }

    @Override
    public List<AtividadeFicha> obterListaAtividadesFicha(String ctx, Integer codigoAtividade) throws ServiceException {
        try {
            return atividadeFichaDao.findListByAttributes(ctx, new String[]{"atividade.codigo"}, new Object[]{codigoAtividade}, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    @Override
    public Long totalAtividades(String ctx) throws ServiceException {
        try {
            return (Long) getAtividadeDao().count(ctx, "codigo", new String[]{}, new Object[]{});
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public List<AtividadeFicha> obterAtividadesDaFicha(String ctx, Integer codigoFicha) throws ServiceException {
        try {
            return atividadeFichaDao.findListByAttributes(ctx, new String[]{"ficha.codigo"}, new Object[]{codigoFicha}, null, 0);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    @Override
    public void atualizaVersaoProgramas(String ctx, Integer codigoAtividade) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("Update  ProgramaTreino set versao = (pt.versao + 1) From ProgramaTreino as pt\n");
            sql.append("INNER JOIN ProgramaTreinoFicha ptf ON ptf.programa_codigo = pt.codigo\n");
            sql.append("INNER JOIN AtividadeFicha atf ON atf.ficha_codigo = ptf.ficha_codigo\n");
            sql.append("WHERE atf.atividade_codigo = ").append(codigoAtividade);
            atividadeFichaDao.executeNativeSQL(ctx,sql.toString());
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }

    }

    @Override
    public void atualizarAtividadesFicha(String ctx, Integer codigoAtividade, String nomeAtividadeAtual, String nomeAtividadeAntigo) throws ServiceException {
        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE AtividadeFicha set nome = ?");
            sql.append(" WHERE atividade_codigo = ?");
            sql.append(" AND nome = ?");
            PreparedStatement stm = atividadeFichaDao.getConnection(ctx).prepareStatement(sql.toString());
            stm.setString(1, nomeAtividadeAtual);
            stm.setInt(2, codigoAtividade);
            stm.setString(3, nomeAtividadeAntigo);
            stm.execute();
        } catch (Exception ex) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_ATUALIZAR_ATIVIDADE_FICHA, ex);
        }
    }

    @Override
    public AtividadeAlteradaDTO atualizarAtividadePorFicha(String ctx, Integer codigoAtividade, Integer codigoAtividadeAntigo, Integer codigoFicha) throws ServiceException {
        AtividadeAlteradaDTO atividadeAlteradaDTO = new AtividadeAlteradaDTO();

        try {
            StringBuilder sql = new StringBuilder();
            sql.append("UPDATE AtividadeFicha set nome = (SELECT nome FROM atividade WHERE codigo = ?), atividade_codigo = ?");
            sql.append(" WHERE codigo = ? and ficha_codigo = ?");
            PreparedStatement stm = atividadeFichaDao.getConnection(ctx).prepareStatement(sql.toString());
            stm.setInt(1, codigoAtividadeAntigo);
            stm.setInt(2, codigoAtividadeAntigo);
            stm.setInt(3, codigoAtividade);
            stm.setInt(4, codigoFicha);
            if(stm.executeUpdate() == 0){
                throw new ServiceException(AtividadeFichaExcecoes.ERRO_BUSCAR_ATIVIDADES_FICHA);
            }

            StringBuilder hql = new StringBuilder();
            Map<java.lang.String, Object> params = new HashMap<>();

            hql.append("SELECT obj FROM AtividadeFicha obj ");
            hql.append("WHERE obj.codigo  :codigoAtividade ");

            params.put("codigoAtividade", codigoAtividade);
            AtividadeFicha atividadeFicha = atividadeFichaDao.findById(ctx, codigoAtividade);

            if(atividadeFicha != null){
                NovaAtividadeFichaDTO novaAtividadeFichaDTO = new NovaAtividadeFichaDTO();
                novaAtividadeFichaDTO.setAtividade(atividadeFicha.getCodigo());
                novaAtividadeFichaDTO.setCodigoAtividade(atividadeFicha.getAtividade().getCodigo());
                novaAtividadeFichaDTO.setOrdem(atividadeFicha.getOrdem());
                novaAtividadeFichaDTO.setIntensidade(atividadeFicha.getIntensidade() != null ? atividadeFicha.getIntensidade().toString() : "");
                novaAtividadeFichaDTO.setSeries(new ArrayList<>());

                for(Serie serie : atividadeFicha.getSeries()){
                    SerieDTO serieDTO = new SerieDTO();
                    serieDTO.setCodSerie(serie.getCodigo());
                    serieDTO.setValor1(serie.getValor1(atividadeFicha.getAtividade().getTipo()));
                    serieDTO.setValor2(serie.getValor2(atividadeFicha.getAtividade().getTipo()));
                    serieDTO.setValor3(serie.getValor3(atividadeFicha.getAtividade().getTipo()));
                    serieDTO.setRepeticaoComp(serie.getRepeticaoComp());
                    serieDTO.setCargaComp(serie.getCargaComp());
                    serieDTO.setComplemento(serie.getComplemento());
                    serieDTO.setCadencia(serie.getCadencia());
                    serieDTO.setOrdem(serie.getOrdem());
                    serieDTO.setRepeticao(serie.getRepeticao());
                    serieDTO.setCarga(serie.getCarga());
                    serieDTO.setDuracao(serie.getDuracao());
                    serieDTO.setDistancia(serie.getDistancia());
                    serieDTO.setVelocidade(serie.getVelocidade());
                    serieDTO.setCodAtividade(atividadeFicha.getAtividade().getCodigo());
                    serieDTO.setTipoAtividade(atividadeFicha.getAtividade().getTipo().getId());
                    serieDTO.setCargaApp(serie.getCargaApp());
                    serieDTO.setRepeticaoApp(serie.getRepeticaoApp());
                    novaAtividadeFichaDTO.getSeries().add(serieDTO);
                }

                novaAtividadeFichaDTO.setCodMetodoExecucao(atividadeFicha.getMetodoExecucao() != null ? atividadeFicha.getMetodoExecucao().getId() : null);
                novaAtividadeFichaDTO.setNomeMetodoExecucao(atividadeFicha.getMetodoExecucao()!= null ? atividadeFicha.getMetodoExecucao().getDescricao() : "");
                novaAtividadeFichaDTO.setCodFicha(codigoFicha);
                atividadeAlteradaDTO.setAtividadeFicha(novaAtividadeFichaDTO);

                AtividadeJSON atividadeBaseDTO = new AtividadeJSON();
                atividadeBaseDTO.setCod(atividadeFicha.getAtividade().getCodigo());
                atividadeBaseDTO.setCodigoAtividade(atividadeFicha.getAtividade().getCodigo());
                atividadeBaseDTO.setNome(atividadeFicha.getAtividade().getNome());
                atividadeBaseDTO.setDescricao(atividadeFicha.getAtividade().getDescricao());
                atividadeBaseDTO.setVersao(atividadeFicha.getAtividade().getVersao().toString());
                atividadeBaseDTO.setTipo(atividadeFicha.getAtividade().getTipo().getId());

                String urlBase = SuperControle.getUrlImagem(ctx);

                if (UteisValidacao.emptyString(atividadeFicha.getAtividade().getFotoKey())) {
                    if (!UteisValidacao.emptyString(atividadeFicha.getAtividade().getURLImg())) {
                        atividadeBaseDTO.setImg(urlBase + MidiasNiveisEnum.MOBILE_RETANGULO.getLocal() + atividadeFicha.getAtividade().getURLImg());
                        atividadeBaseDTO.setThumb(urlBase + MidiasNiveisEnum.MOBILE_QUADRADA.getLocal() + atividadeFicha.getAtividade().getURLImg());
                        atividadeBaseDTO.setImgMedium(urlBase + MidiasNiveisEnum.MEDIA_QUADRADA.getLocal() + atividadeFicha.getAtividade().getURLImg());
                    }
                } else {
                    atividadeBaseDTO.setImg(atividadeFicha.getAtividade().getFotoKey());
                    atividadeBaseDTO.setThumb(atividadeFicha.getAtividade().getFotoKeyMin());
                    atividadeBaseDTO.setImgMedium(atividadeFicha.getAtividade().getFotoKeyPequena());
                }

                List<String> imagensAssociadas = atividadeFicha.getImagemAtiviadeAssocidada(urlBase, ctx);

                if (MetodoExecucaoEnum.BI_SET.equals(atividadeFicha.getMetodoExecucao()) || MetodoExecucaoEnum.TRI_SET.equals(atividadeFicha.getMetodoExecucao()) || (imagensAssociadas != null && imagensAssociadas.size() > 0)) {
                    atividadeBaseDTO.setImgMediumUrls(imagensAssociadas);
                } else if(atividadeBaseDTO.getImg() != null) {
                    List<String> imgMedionURLs = new ArrayList<>();
                    imgMedionURLs.add(atividadeBaseDTO.getImg());
                    atividadeBaseDTO.setImgMediumUrls(imgMedionURLs);
                }

                atividadeBaseDTO.setUrlVideo(atividadeFicha.getAtividade().getLinkVideo());
                if (atividadeFicha.getBiSet() || atividadeFicha.getTriSet()){
                    atividadeBaseDTO.setUrlVideos(atividadeFicha.getAtividade().getListaVideos(atividadeFicha));
                }
                atividadeBaseDTO.setOrdem(atividadeFicha.getOrdem());
                if(atividadeFicha.getAtividade().getAtividadesAlternativas() != null && atividadeBaseDTO.getAtividadesAlternativas() != null){
                    for(AtividadeAlternativa aa : atividadeFicha.getAtividade().getAtividadesAlternativas()){
                        atividadeBaseDTO.getAtividadesAlternativas().get(aa.getAtividadeAlternativa());
                    }
                }

                atividadeBaseDTO.setUrlVideo(atividadeFicha.getAtividade().getLinkVideo());
                atividadeBaseDTO.setUrlVideos(atividadeFicha.getAtividade().getListaVideos(atividadeFicha));

                StringBuilder aparelhos = new StringBuilder();
                for (AtividadeAparelho atap : atividadeFicha.getAtividade().getAparelhos()) {
                    aparelhos.append(atap.getAparelho().obterNome(1)).append(" / ");
                }
                int indBarra = aparelhos.lastIndexOf("/");
                if (aparelhos.length() > 0) {
                    atividadeBaseDTO.setAparelhos(aparelhos.substring(0, indBarra - 1));
                }

                List<String> categoriasAtividade = new ArrayList<>();
                for(AtividadeCategoriaAtividade ca : atividadeFicha.getAtividade().getCategorias()) {
                    categoriasAtividade.add(ca.getCategoriaAtividade().getNome());
                }
                atividadeBaseDTO.setCategorias(categoriasAtividade);

                List<String> gruposMusculares = new ArrayList<>();
                for(AtividadeGrupoMuscular gm : atividadeFicha.getAtividade().getGruposMusculares()) {
                    gruposMusculares.add(gm.getGrupoMuscular().getNome());
                }
                atividadeBaseDTO.setGruposMusculares(gruposMusculares);

                List<String> niveis = new ArrayList<>();
                for(AtividadeNivel n : atividadeFicha.getAtividade().getNiveis()) {
                    niveis.add(n.getNivel().getNome());
                }
                atividadeBaseDTO.setNiveis(niveis);

                List<String> animacoes = new ArrayList<>();
                for(AtividadeAnimacao a : atividadeFicha.getAtividade().getAnimacoes()) {
                    if(a.getAnimacao() != null) {
                        animacoes.add(a.getAnimacao().getTitulo());
                    }
                }
                atividadeBaseDTO.setAnimacoes(animacoes);

                List<String> aparelhosAtividade = new ArrayList<>();
                for(AtividadeAparelho a : atividadeFicha.getAtividade().getAparelhos()) {
                    aparelhosAtividade.add(a.getAparelho().getNome());
                }
                atividadeBaseDTO.setListAparelhos(aparelhosAtividade);

                atividadeAlteradaDTO.setAtividadeBase(atividadeBaseDTO);
                atividadeAlteradaDTO.setAtividadeFicha(novaAtividadeFichaDTO);
            }

        } catch (ServiceException e) {
            throw e;
        } catch (Exception ex) {
            throw new ServiceException(AtividadeFichaExcecoes.ERRO_ATUALIZAR_ATIVIDADE_FICHA, ex);
        }

        return atividadeAlteradaDTO;
    }

    @Override
    public Map<String, Integer> obterMapaAtividades(String ctx) throws ServiceException{
        try {
            Map<String, Integer> mapa = new HashMap<String, Integer>();
            List<Atividade> all = getAtividadeDao().findAll(ctx);
            for(Atividade a : all){
                mapa.put(a.getNome(), a.getCodigo());
            }
            return mapa;
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void salvarMidiaNuvem(String chave, List<UploadedFile> objs, Atividade atividade) throws Exception {
        for(UploadedFile o : objs){
            AtividadeAnimacao aa = new AtividadeAnimacao();
            aa.setAtividade(atividade);
            aa = atvAnimacaoDao.insert(chave, aa);
            byte[] imagem = animacaoService.redimensionarImagemGrande(o, 1024);
            String key = MidiaService.getInstanceWood().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_ATIVIDADE, aa.getCodigo().toString(), imagem);
            aa.setFotoKey(key);
            byte[] imagemPequena = animacaoService.redimensionarImagemGrande(o, 375);
            String keyPequena = MidiaService.getInstanceWood().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_ATIVIDADE, aa.getCodigo().toString().concat("peq"), imagemPequena);
            aa.setFotoKeyPequena(keyPequena);
            byte[] imagemMiniatura = animacaoService.redimensionarImagemGrande(o, 75);
            String keyMiniatura = MidiaService.getInstanceWood().uploadObjectFromByteArray(chave, MidiaEntidadeEnum.FOTO_ATIVIDADE, aa.getCodigo().toString().concat("min"), imagemMiniatura);
            aa.setFotoKeyMiniatura(keyMiniatura);
            atvAnimacaoDao.update(chave, aa);
        }
    }

    public void removerImagem(String chave, AtividadeAnimacao aa) throws Exception{
        MidiaService.getInstanceWood().deleteObject(aa.getFotoKey());
        atvAnimacaoDao.delete(chave, aa);
    }

    public void refresh(String ctx, Atividade object) throws Exception{
        getAtividadeDao().refresh(ctx, object);
    }

    public List<AtividadeAnimacao> consultarImagens(String key, Integer atividade) throws Exception{
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("atividade", atividade);
        return      atvAnimacaoDao.findByParam(key,
                "select obj from AtividadeAnimacao obj where obj.atividade.codigo = :atividade", params);
    }


    public List<AtividadeAnimacao> obterImagens(String ctx, Integer codigoAtividade) throws ServiceException{
        return atvAnimacaoDao.obterImagens(ctx, codigoAtividade);
    }

    public List<AtividadeVideo> obterLinkVideos(String ctx, Integer codigoAtividade) throws ServiceException{
        return atividadeVideoDao.obterLinkVideos(ctx, codigoAtividade);
    }

    private void preencherAtividade(String ctx, Atividade atividade) throws Exception {
        atividade.getAnimacoes().clear();
        atividade.setAnimacoes(obterImagens(ctx, atividade.getCodigo()));
        atividade.setLinkVideos(obterLinkVideos(ctx,atividade.getCodigo()));
        StringBuilder where = new StringBuilder("where obj.atividade.codigo = :codigo");
        Map<String, Object> filtro = new HashMap<String, Object>();
        filtro.put("codigo", atividade.getCodigo());
        atividade.getEmpresasHabilitadas().clear();
        atividade.setEmpresasHabilitadas(atividadeEmpresaDao.findByParam(ctx, where, filtro));
        atividade.getCategorias().clear();
        atividade.setCategorias(atividadeCategoriaAtividadeDao.findByParam(ctx, where, filtro));
        atividade.getAparelhos().clear();
        atividade.setAparelhos(atividadeAparelhoDao.findByParam(ctx, where, filtro));
        atividade.getGruposMusculares().clear();
        atividade.setGruposMusculares(atividadeGrupoMuscularDao.findByParam(ctx, where, filtro));
        atividade.getMusculos().clear();
        atividade.setMusculos(atividadeMusculoDao.findByParam(ctx, where, filtro));
        atividade.getNiveis().clear();
        atividade.setNiveis(atividadeNivelDao.findByParam(ctx, where, filtro));
    }

    @Override
    public List<AtividadeSimplesResponseTO> listarAtividadesMontagemTreino(Integer ficha,FiltroAtividadeJSON filtroAtividadeJSON, Boolean comGrupo, String tipo, PaginadorDTO paginadorDTO, boolean crossfit, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,ASC");
            }
            List<Integer> atividadesFicha = new ArrayList<>();
            if(ficha != null){
                ResultSet rs = atividadeFichaDao.createStatement(ctx, "select atividade_codigo from atividadeficha where ficha_codigo = " + ficha);
                    while (rs.next()) {
                        atividadesFicha.add(rs.getInt("atividade_codigo"));
                    }

            }
            List<AtividadeSimplesResponseTO> listaAtividades = new ArrayList<>();
            Map<CadastrosAuxiliaresEnum, List<Integer>> params = new HashMap() {{
                put(CadastrosAuxiliaresEnum.APARELHO, filtroAtividadeJSON.getAparelhosIds());
                put(CadastrosAuxiliaresEnum.EMPRESA, filtroAtividadeJSON.getEmpresasIds());
                put(CadastrosAuxiliaresEnum.CATEGORIA_ATIVIDADE, filtroAtividadeJSON.getCategoriasIds());
                put(CadastrosAuxiliaresEnum.GRUPOS_MUSCULARES, filtroAtividadeJSON.getGrupoMuscularesIds());
                put(CadastrosAuxiliaresEnum.NIVEL, filtroAtividadeJSON.getNiveisIds());
            }};
            List<Atividade> lista = consultaAtividadesMontagem(ctx, params, comGrupo, filtroAtividadeJSON.getParametro(),
                    filtroAtividadeJSON.getAvoid(),
                    empresaId, paginadorDTO);

            try {
                if (lista != null && !lista.isEmpty()) {
                    List<Integer> codigosAtividades = lista.stream().map(Atividade::getCodigo).collect(Collectors.toList());

                    Map<Integer, List<GrupoMuscularResponseTO>> gruposMuscularesPorAtividade =
                            grupoMuscularService.consultarGruposMuscularesPorAtividades(ctx, codigosAtividades);

                    for (Atividade atividade : lista) {
                        AtividadeSimplesResponseTO atividadeSimplesResponseTO =
                                new AtividadeSimplesResponseTO(atividade, atividadesFicha.contains(atividade.getCodigo()));

                        List<GrupoMuscularResponseTO> grupoMuscularResponseTOS =
                                gruposMuscularesPorAtividade.getOrDefault(atividade.getCodigo(), new ArrayList<>());

                        atividadeSimplesResponseTO.setGruposMusculares(grupoMuscularResponseTOS);
                        listaAtividades.add(atividadeSimplesResponseTO);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();

                listaAtividades = new ArrayList<>();
                if (lista != null && !lista.isEmpty()) {
                    for (Atividade atividade : lista) {
                        listaAtividades.add(new AtividadeSimplesResponseTO(atividade, atividadesFicha.contains(atividade.getCodigo())));
                    }
                }
            }

            return listaAtividades;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
    }

    @Override
    public List<AtividadeCompletaResponseTO> listarAtividades(FiltroAtividadeJSON filtroAtividadeJSON,String tipo, PaginadorDTO paginadorDTO, boolean crossfit, Integer empresaId) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            if (!StringUtils.isBlank(paginadorDTO.getSort())) {
                paginadorDTO.setSort(paginadorDTO.getSort());
            } else {
                paginadorDTO.setSort("nome,"+filtroAtividadeJSON.getOrdenacao());
            }

            List<Atividade> lista = atividadeDao.listarAtividades(ctx, filtroAtividadeJSON, tipo, paginadorDTO, crossfit, true, new ArrayList<>(), empresaId);
            List<AtividadeCompletaResponseTO> listaRet = new ArrayList<>();
            if (lista != null) {
                for (Atividade atividade : lista) {
//                    preencherAtividade(ctx, atividade);
                    atividade.setAnimacoes(obterImagens(ctx, atividade.getCodigo()));
                    listaRet.add(new AtividadeCompletaResponseTO(atividade));
                }
            }
            return listaRet;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
    }

    @Override
    public AtividadeCompletaResponseTO buscarAtividade(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Atividade atividade = new Atividade();
            atividade = obterPorId(ctx, id);
            if (atividade == null) {
                throw new ServiceException(AtividadeExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            preencherAtividade(ctx, atividade);
            atividade.setAnimacoes(obterImagens(ctx, atividade.getCodigo()));
            AtividadeCompletaResponseTO ret = new AtividadeCompletaResponseTO(atividade);
            atividadeRelacionadas(ctx, atividade, ret);
            return ret;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADE, e);
        }
    }

    private void atividadeRelacionadas(String ctx, Atividade atv,
                                       AtividadeCompletaResponseTO ret ) throws Exception{
        if(atv.getAtividadesAlternativas() == null  || atv.getAtividadesAlternativas().isEmpty()){
            return;
        }
        String str = "";
        for (AtividadeAlternativa a : atv.getAtividadesAlternativas()) {
            str += "," + a.getAtividadeAlternativa();
        }
        ResultSet rs = atividadeDao.createStatement(ctx,
                " select codigo, nome from Atividade " +
                        " where codigo in (" + str.replaceFirst(",", "") + ") " +
                        " order by codigo");
            ret.setAtividadesRelacionadas(new ArrayList<>());
            while (rs.next()) {
                AtividadeRelacionadaResponseTO relacionada = new AtividadeRelacionadaResponseTO();
                relacionada.setId(rs.getInt("codigo"));
                relacionada.setNome(rs.getString("nome"));
                List<AtividadeAnimacao> atividadeImagemResponseTOS = obterImagens(ctx, relacionada.getId());
                relacionada.setImagem(new ArrayList<>());

                for(AtividadeAnimacao atividadeAnimacao : atividadeImagemResponseTOS){
                    relacionada.getImagem().add(new AtividadeImagemResponseTO(atividadeAnimacao));
                }

                ret.getAtividadesRelacionadas().add(relacionada);
            }
        }
    @Override
    public List<AtividadeEmpresaResponseTO> configsEmpresa() throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            AtividadeEmpresaService emp = UtilContext.getBean(AtividadeEmpresaService.class);
            List<AtividadeEmpresa> atividadeEmpresas = emp.obterTodos(ctx);
            return new ArrayList(){{
                for (AtividadeEmpresa ae : atividadeEmpresas){
                    if(!UteisValidacao.emptyString(ae.getIdentificador())){
                        add(new AtividadeEmpresaResponseTO(ae));
                    }

                }
            }};
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADE, e);
        }
    }

    @Override
    public List<AtividadeCompletaResponseTO> listarTodasAtividades(FiltroAtividadeCrossfitJSON filtroAtividadeCrossfitJSON) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Atividade> lista = atividadeDao.listarAtividadeSelect(ctx, filtroAtividadeCrossfitJSON);

            List<AtividadeCompletaResponseTO> listaRet = new ArrayList<>();
            if (lista != null) {
                for (Atividade atividade : lista) {
                    atividade.setAnimacoes(obterImagens(ctx, atividade.getCodigo()));
                    listaRet.add(new AtividadeCompletaResponseTO(atividade));
                }
            }
            /**
             * Contando total de atividades filtradas
             *
             * filtroAtividadeCrossfitJSON.setMaxResults(null);
             * filtroAtividadeCrossfitJSON.setIndex(null);
             * List<Atividade> atividadesFiltradas = atividadeDao.listarAtividadeSelect(ctx, filtroAtividadeCrossfitJSON);
             */
            /**
             * Harlei vai arrumar o que falta na parte do front
             *
             * return new AtividadeSelectResponseDTO(atividadesFiltradas.size(), listaRet);
             */
            return listaRet;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_BUSCAR_ATIVIDADES, e);
        }
    }

    public void salvarMidiaNuvemEndpoint(String chave, List<AtividadeImagemUploadTO> objs, Atividade atividade) throws Exception {
        for(AtividadeImagemUploadTO o : objs){
            AtividadeAnimacao aa = new AtividadeAnimacao();
            MidiaEntidadeEnum midia = o.getNome().endsWith(".gif") ? MidiaEntidadeEnum.GIF_ATIVIDADE : MidiaEntidadeEnum.FOTO_ATIVIDADE;
            aa.setAtividade(atividade);
            aa.setProfessor(o.getProfessor());
            aa = atvAnimacaoDao.insert(chave, aa);
            Date agora = new Date();

            if (o.getNome().endsWith(".gif")) {
                String key = MidiaService.getInstanceWood().uploadObjectFromByteArray(chave, midia, aa.getCodigo().toString(), o.getData());
                if (!UteisValidacao.emptyString(key)) {
                    key = key  + "?time=" + agora.getTime();
                }
                aa.setFotoKey(key);
                aa.setFotoKeyPequena(key);
                aa.setFotoKeyMiniatura(key);
            } else {
                byte[] imgAnterior = o.getData();

                if (imgAnterior != null) {
                    int[][] sizes = {{1000, 30}, {375, 30}, {75, 30}}; // Largura limite e qualidade para cada tamanho
                    String[] keys = {"", "peq", "min"}; // Sufixos para as chaves das imagens redimensionadas

                    for (int i = 0; i < sizes.length; i++) {
                        int widthLimit = sizes[i][0];
                        int quality = sizes[i][1];

                        if (ResizeImage.isReduzirImagem(imgAnterior, widthLimit)) {
                            byte[] reduzirImgAnterior = ResizeImage.reduzirImagem(imgAnterior, quality);
                            byte[] comprimirImgAnterior = ResizeImage.comprimirImagem(reduzirImgAnterior, 0.3f);
                            String key = MidiaService.getInstanceWood().uploadObjectFromByteArray(chave, midia, aa.getCodigo().toString().concat(keys[i]), comprimirImgAnterior);
                            if (!UteisValidacao.emptyString(key)) {
                                key = key  + "?time=" + agora.getTime();
                            }
                            switch (i) {
                                case 0:
                                    aa.setFotoKey(key);
                                    break;
                                case 1:
                                    aa.setFotoKeyPequena(key);
                                    break;
                                case 2:
                                    aa.setFotoKeyMiniatura(key);
                                    break;
                            }
                        } else {
                            String key = MidiaService.getInstanceWood().uploadObjectFromByteArray(chave, midia, aa.getCodigo().toString().concat(keys[i]), imgAnterior);
                            if (!UteisValidacao.emptyString(key)) {
                                key = key  + "?time=" + agora.getTime();
                            }
                            switch (i) {
                                case 0:
                                    aa.setFotoKey(key);
                                    break;
                                case 1:
                                    aa.setFotoKeyPequena(key);
                                    break;
                                case 2:
                                    aa.setFotoKeyMiniatura(key);
                                    break;
                            }
                        }
                    }
                }
            }

            aa = atvAnimacaoDao.update(chave, aa);
            atividade.getAnimacoes().add(aa);
        }
    }

    private Atividade cadastrarAtualizarAtividade(String ctx, AtividadeTO atividadeTO, Boolean nova) throws ServiceException {

        try {
            Atividade atividade = null;
            List<AtividadeEmpresa> empresas = new ArrayList<>();
            if (nova) {
                atividade = new Atividade();
                if (atividadeDao.exists(ctx, atividadeTO, "nome")) {
                    throw new ServiceException(AtividadeExcecoes.VALIDACAO_ATIVIDADE_JA_EXISTE);
                }
            } else {
                String[] strIdAtividade = new String[]{"atividade.codigo"};
                Object[] objIdAtividade = new Object[]{atividadeTO.getId()};
                atividadeEmpresaDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                atividadeCategoriaAtividadeDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                atividadeAparelhoDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                atividadeGrupoMuscularDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                atividadeMusculoDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                atividadeNivelDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                atividadeAlternativaDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                boolean limparLista = verificarListaImagens(atividadeTO);
                if (!limparLista || UteisValidacao.emptyList(atividadeTO.getImages())) {
                    atvAnimacaoDao.deleteComParam(ctx, strIdAtividade, objIdAtividade);
                }
                atividade = obterPorId(ctx, atividadeTO.getId());
                preencherAtividade(ctx, atividade);
            }
            if (atividade == null) {
                throw new ServiceException(AtividadeExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            if (atividadeTO.getEmpresas() == null || atividadeTO.getEmpresas().size() == 0) {
                atividade.setTodasEmpresas(true);
            } else {
                atividade.setTodasEmpresas(false);
                for (AtividadeEmpresaTO aeTO : atividadeTO.getEmpresas()) {
                    if (aeTO.getEmpresa() != null && aeTO.getEmpresa().getId() != null) {
                        Empresa empresa = empresaDao.findById(ctx, aeTO.getEmpresa().getId());
                        if (empresa == null) {
                            throw new ServiceException(AtividadeExcecoes.EMPRESA_NAO_ENCONTRADA);
                        }
                        AtividadeEmpresa ae = new AtividadeEmpresa(atividade, empresa);
                        ae.setIdentificador(aeTO.getIdentificador());
                        empresas.add(ae);
                    }
                }
                atividade.getEmpresasHabilitadas().clear();
                atividade.setEmpresasHabilitadas(empresas);
            }
            if (atividadeTO.getAtividadesRelacionadas() != null && atividadeTO.getAtividadesRelacionadas().size() > 0) {
                List<AtividadeAlternativa> alternativas = new ArrayList<>();
                for (Integer id : atividadeTO.getAtividadesRelacionadas()) {
                    AtividadeAlternativa aca = new AtividadeAlternativa();
                    aca.setAtividade(atividade);
                    aca.setAtividadeAlternativa(id);
                    alternativas.add(aca);
                }
                atividade.getAtividadesAlternativas().clear();
                atividade.setAtividadesAlternativas(alternativas);
            }
            if (atividadeTO.getCategoriaAtividadeIds() != null && atividadeTO.getCategoriaAtividadeIds().size() > 0) {
                List<AtividadeCategoriaAtividade> categorias = new ArrayList<>();
                for (Integer id : atividadeTO.getCategoriaAtividadeIds()) {
                    CategoriaAtividade ca = categoriaAtividadeDao.findById(ctx, id);
                    if (ca == null) {
                        throw new ServiceException(AtividadeExcecoes.CATEGORIA_NAO_ENCONTRADA);
                    }
                    AtividadeCategoriaAtividade aca = new AtividadeCategoriaAtividade(ca, atividade);
                    categorias.add(aca);
                }
                atividade.getCategorias().clear();
                atividade.setCategorias(categorias);
            }
            if (atividadeTO.getAparelhoIds() != null && atividadeTO.getAparelhoIds().size() > 0) {
                List<AtividadeAparelho> aparelhos = new ArrayList<>();
                for (Integer id : atividadeTO.getAparelhoIds()) {
                    Aparelho aparelho = aparelhoDao.findById(ctx, id);
                    if (aparelho == null) {
                        throw new ServiceException(AtividadeExcecoes.APARELHO_NAO_ENCONTRADO);
                    }
                    AtividadeAparelho aa = new AtividadeAparelho(aparelho, atividade);
                    aparelhos.add(aa);
                }
                atividade.getAparelhos().clear();
                atividade.setAparelhos(aparelhos);
            }
            if (atividadeTO.getGrupoMuscularIds() != null && atividadeTO.getGrupoMuscularIds().size() > 0) {
                List<AtividadeGrupoMuscular> grupos = new ArrayList<>();
                for (Integer id : atividadeTO.getGrupoMuscularIds()) {
                    GrupoMuscular grupo = grupoMuscularDao.findById(ctx, id);
                    if (grupo == null) {
                        throw new ServiceException(AtividadeExcecoes.GRUPO_MUSCULAR_NAO_ENCONTRADO);
                    }
                    AtividadeGrupoMuscular agm = new AtividadeGrupoMuscular(grupo, atividade);
                    grupos.add(agm);
                }
                atividade.getGruposMusculares().clear();
                atividade.setGruposMusculares(grupos);
            }
            if (atividadeTO.getMusculoIds() != null && atividadeTO.getMusculoIds().size() > 0) {
                List<AtividadeMusculo> musculos = new ArrayList<>();
                for (Integer id : atividadeTO.getMusculoIds()) {
                    Musculo musculo = musculoDao.findById(ctx, id);
                    if (musculo == null) {
                        throw new ServiceException(AtividadeExcecoes.MUSCULO_NAO_ENCONTRADO);
                    }
                    AtividadeMusculo am = new AtividadeMusculo(musculo, atividade);
                    musculos.add(am);
                }
                atividade.getMusculos().clear();
                atividade.setMusculos(musculos);
            }
            if (atividadeTO.getNivelIds() != null && atividadeTO.getNivelIds().size() > 0) {
                List<AtividadeNivel> niveis = new ArrayList<>();
                for (Integer id : atividadeTO.getNivelIds()) {
                    Nivel nivel = nivelDao.findById(ctx, id);
                    if (nivel == null) {
                        throw new ServiceException(AtividadeExcecoes.NIVEL_NAO_ENCONTRADO);
                    }
                    AtividadeNivel an = new AtividadeNivel(nivel, atividade);
                    niveis.add(an);
                }
                atividade.getNiveis().clear();
                atividade.setNiveis(niveis);
            }

            atividade.setNome(atividadeTO.getNome());
            atividade.setAtivo(atividadeTO.getAtiva());
            atividade.setUsarNaPrescricao(atividadeTO.getUsarNaPrescricao());
            atividade.setSeriesApenasDuracao(atividadeTO.getSerieApenasDuracao());
            atividade.setTipo(atividadeTO.getTipo().getTipoAtividadeEnum());
            atividade.setDescricao(atividadeTO.getDescricao());
            atividade.setLinkVideo(atividadeTO.getVideoUri());
            atividade.setCrossfit(atividadeTO.getCrossfit());
            if (nova) {
                atividade = inserir(ctx, atividade);

            } else {
                if(atividade.getIdIA() != null){
                    try {
                        atividade.setEditadoPor(sessaoService.getUsuarioAtual().getUsername());
                        atividade.setUltimaEdicao(new Date().getTime());
                    }catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                atividade = alterar(ctx, atividade);
                StringBuilder hql = new StringBuilder();
                atividade.getAnimacoes().clear();
                hql.append("DELETE FROM AtividadeAnimacao where atividade_codigo  = " + atividade.getCodigo() + " and animacao_codigo is not null");
                atvAnimacaoDao.executeQuery(ctx, hql.toString());
            }
            if (atividadeTO.getImages() != null && atividadeTO.getImages().size() > 0) {
                List<AtividadeAnimacao> animacaoArrayList = new ArrayList<AtividadeAnimacao>();
                List<AtividadeAnimacao> animacaoArmazenada = new ArrayList<>();
                animacaoArmazenada.addAll(atividade.getAnimacoes());
                for (AtividadeImagemTO imagemTO : atividadeTO.getImages()) {
                    AtividadeAnimacao atividadeAnimacao = new AtividadeAnimacao();
                    if (imagemTO.getType().equals(TipoAtividadeImagemEnum.UPLOAD)) {
                        atividadeAnimacao = atvAnimacaoDao.findById(ctx, Integer.valueOf(imagemTO.getId()));
                        for (AtividadeAnimacao animacaoUpload : animacaoArmazenada) {
                            if (animacaoUpload.getCodigo().equals(Integer.valueOf(imagemTO.getId()))) {
                                atividade.getAnimacoes().remove(animacaoUpload);
                            }
                        }
                        atividadeAnimacao.setProfessor(imagemTO.getProfessor());
                    } else {
                        Animacao animacao;
                        try {
                            Integer id = Integer.valueOf(imagemTO.getId());
                            animacao = animacaoService.obterPorId(ctx, id);
                        } catch (Exception e) {
                            animacao = animacaoService.obterAnimacaoPorNomeArquivo(ctx, imagemTO.getId());

                            if (animacao == null || UteisValidacao.emptyNumber(animacao.getCodigo())) {
                                List<Animacao> animacoes = animacaoService.obterTodos(ctx);
                                for (Animacao imgCatalago : animacoes) {
                                    if (imagemTO.getId().equals(imgCatalago.getUrl())) {
                                        animacao = animacaoService.inserir(ctx, imgCatalago);
                                        break;
                                    }
                                }
                            }
                        }

                        if (animacao != null && !UteisValidacao.emptyNumber(animacao.getCodigo())) {
                            atividadeAnimacao.setAnimacao(animacao);
                            atividadeAnimacao.setProfessor(imagemTO.getProfessor());
                        }
                    }
                    atividadeAnimacao.setAtividade(atividade);
                    animacaoArrayList.add(atividadeAnimacao);
                }
                for (AtividadeAnimacao animacaoUpload : atividade.getAnimacoes()) {
                    StringBuilder hql = new StringBuilder();
                    hql.append("DELETE FROM AtividadeAnimacao where codigo  = " + animacaoUpload.getCodigo());
                    atvAnimacaoDao.executeQuery(ctx, hql.toString());
                }
                atividade.getAnimacoes().clear();
                atividade.setAnimacoes(animacaoArrayList);
                alterar(ctx, atividade);
            }
            if (atividadeTO.getImageUploads() != null && atividadeTO.getImageUploads().size() > 0) {
                salvarMidiaNuvemEndpoint(ctx, atividadeTO.getImageUploads(), atividade);
            }
            List<AtividadeVideo> listaLinkVideosSalvos = atividadeVideoDao.obterLinkVideos(ctx,atividade.getCodigo());

            for(AtividadeVideo a : listaLinkVideosSalvos){
                if(!atividadeTO.getLinkVideos().contains(new AtividadeVideoTO(a))){
                    atividadeVideoDao.delete(ctx, a);
                }
            }

            if(atividadeTO.getLinkVideos() != null && atividadeTO.getLinkVideos().size() > 0){
                for(AtividadeVideoTO atividadeVideoTO : atividadeTO.getLinkVideos()){
                    if(atividadeVideoTO.getId() != null && atividadeVideoTO.getId() > 0){
                        AtividadeVideo atividadeVideo = atividadeVideoDao.findById(ctx, atividadeVideoTO.getId());
                        if(atividadeVideo !=null){
                           atividadeVideo.setLinkvideo(atividadeVideoTO.getLinkVideo());
                           atividadeVideo.setProfessor(atividadeVideoTO.getProfessor());
                           atividadeVideoDao.update(ctx, atividadeVideo);
                        }else{
                            atividadeVideoTO.setId(null);
                            atividadeVideo = new AtividadeVideo(atividadeVideoTO, atividade);
                            atividadeVideoDao.insert(ctx,atividadeVideo);
                        }
                    }else{
                        atividadeVideoTO.setId(null);
                        AtividadeVideo novaAtividadeVideo = new AtividadeVideo(atividadeVideoTO, atividade);
                        atividadeVideoDao.insert(ctx,novaAtividadeVideo);
                    }
                }
            }

            purgeAtividadesCache(ctx);
            return atividade;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_INCLUIR_ATIVIDADE, e);
        }
    }

    @Override
    public AtividadeCompletaResponseTO cadastrarAtividade(AtividadeTO atividadeTO) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            atividadeTO.setNome(atividadeTO.getNome().trim());
            if(atividadeDao.exists(ctx,atividadeTO,"nome")){
                throw new ServiceException(AtividadeExcecoes.VALIDACAO_ATIVIDADE_JA_EXISTE);
            }
            Atividade atividade = cadastrarAtualizarAtividade(ctx, atividadeTO, true);
            incluirLog(ctx, atividade.getCodigo().toString(), "",
                    "", atividade.getDescricaoParaLog(null), "INCLUSÃO",
                    "INCLUSÃO DE ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade", sessaoService, logDao);
            return new AtividadeCompletaResponseTO(atividade);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public AtividadeCompletaResponseTO atualizarAtividade(AtividadeTO atividadeTO) throws ServiceException {
        Atividade atvAntesAlteracao;

        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            atividadeTO.setNome(atividadeTO.getNome().trim());
            if(atividadeDao.exists(ctx,atividadeTO,"nome", "id")){
                throw new ServiceException(AtividadeExcecoes.VALIDACAO_ATIVIDADE_JA_EXISTE);
            }
            atvAntesAlteracao = atividadeDao.findById(ctx, atividadeTO.getId());
            Atividade atividade = cadastrarAtualizarAtividade(ctx, atividadeTO, false);
            incluirLog(ctx, atividade.getCodigo().toString(),
                    "", atvAntesAlteracao.getDescricaoParaLog(atividade),
                    atividade.getDescricaoParaLog(atvAntesAlteracao), "ALTERAÇÃO",
                    "ALTERAÇÃO DE ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade",
                    sessaoService, logDao, !UteisValidacao.emptyString(atividade.getIdIA()));
            return new AtividadeCompletaResponseTO(atividade);
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public void atualizarSituacaoAtividade(AtividadeTO atividadeTO, Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Atividade atividade = obterPorId(ctx, id);
            if(atividade != null){
                if(atividadeTO.getAtiva() == true){
                    atividade.setAtivo(false);
                }else{
                    atividade.setAtivo(true);
                }
                Atividade descAnterior = UtilReflection.copy(atividade);
                incluirLog(ctx, atividade.getCodigo().toString(),
                        null, descAnterior.getDescricaoParaLog(atividade),
                        atividade.getDescricaoParaLog(descAnterior), "INATIVAR",
                        "INATIVAR ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade",
                        sessaoService, logDao, !UteisValidacao.emptyString(atividade.getIdIA()));
            }
            alterar(ctx, atividade);


        } catch (ServiceException e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_ATUALIZAR_ATIVIDADE, e);
        }
    }

    @Override
    public void removerAtividade(Integer id) throws ServiceException {
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            Atividade atividade = obterPorId(ctx, id);
            if (atividade == null) {
                throw new ServiceException(AtividadeExcecoes.ATIVIDADE_NAO_ENCONTRADA);
            }
            excluir(ctx, atividade);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_EXCLUIR_ATIVIDADE, e);
        }
    }

    protected void prepare(final String ctx) throws ServiceException {
        EntityManagerService ems = (EntityManagerService) UtilContext.getBean(EntityManagerService.class);
        try {
            ems.closeQuietly(ctx);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    public boolean verificarListaImagens(AtividadeTO atividadeTO){
        for (AtividadeImagemTO imagemTO : atividadeTO.getImages()) {
            if(imagemTO.getType().equals(TipoAtividadeImagemEnum.UPLOAD)){
                return true;
            }
        }
        return false;
    }

    @Override
    public void inativar(final String ctx, Atividade object) throws ServiceException {
        try {
            object.setAtivo(false);
            getAtividadeDao().update(ctx, object);
        } catch (Exception ex) {
            throw new ServiceException(ex.getMessage());
        }
    }

    public void montarImagens(String ctx, Atividade object) throws Exception{
        object.setAnimacoes(obterImagens(ctx, object.getCodigo()));
        for(AtividadeAnimacao aa : object.getAnimacoes()){
            if(aa.getAnimacao() != null && !UteisValidacao.emptyNumber(aa.getAnimacao().getCodigo())){
                aa.setAnimacao(animacaoDao.findById(ctx, aa.getAnimacao().getCodigo()));
            }
        }
    }

    public boolean isAtividadesCached(final String ctx, boolean crossfit) {
        return (mapTodasAtividades.containsKey(ctx)
                && mapTodasAtividades.get(ctx) != null
                && !mapTodasAtividades.get(ctx).isEmpty())
                || (mapTodasAtividadesCross.containsKey(ctx)
                && mapTodasAtividadesCross.get(ctx) != null
                && !mapTodasAtividadesCross.get(ctx).isEmpty());
    }

    public void purgeAtividadesCache(final String ctx) {
        mapTodasAtividades.clear();
        mapTodasAtividadesCross.clear();
    }

    private void addGif(String chave, Atividade atividade, AtividadeJSON atvIA ) throws Exception{
        AtividadeAnimacao aa = new AtividadeAnimacao();
        aa.setAtividade(atividade);
        String key = atvIA.getImg().replace(Aplicacao.getProp(Aplicacao.urlFotosNuvem) + "/", "");
        aa.setFotoKey(key);
        aa.setFotoKeyPequena(key);
        aa.setFotoKeyMiniatura(key);
        aa = atvAnimacaoDao.insert(chave, aa);
    }

    public void toggleAtividadesIA(Boolean marcar) throws ServiceException{
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            List<Atividade> all = atividadeDao.findAll(ctx);
            for(Atividade atividade : all){
                if(UteisValidacao.emptyString(atividade.getIdIA())){
                    continue;
                }
                Atividade atvAntesAlteracao = atividade.clone();
                boolean gravarLog = false;
                if(atividade.getUsarNaPrescricao() && !marcar){
                    gravarLog = true;
                    atividade.setUsarNaPrescricao(false);
                    atividadeDao.update(ctx, atividade);
                } else if(!atividade.getUsarNaPrescricao() && marcar){
                    gravarLog = true;
                    atividade.setUsarNaPrescricao(true);
                    atividadeDao.update(ctx, atividade);
                }

                if(gravarLog){
                    incluirLog(ctx, atividade.getCodigo().toString(), "",
                            atvAntesAlteracao.getDescricaoParaLog(atividade),
                            atividade.getDescricaoParaLog(atvAntesAlteracao),
                            "ALTERAÇÃO", "ALTERAÇÃO DE ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade",
                            sessaoService.getUsuarioAtual().getUsername() + " - via configurações ", logDao, true);
                }
            }
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    @Override
    public Atividade obterPorIdIAVersaoDois(String ctx, int idIA) throws Exception {
        return atividadeDao.obterPorIdIAVersaoDois(ctx, idIA);
    }

    @Override
    public Atividade buscarPorNomeOriginalIA(String ctx, String nomeOriginalIA) throws Exception {
        return atividadeDao.buscarPorNomeOriginalIA(ctx, nomeOriginalIA);
    }

    @Override
    public Atividade buscarPorNomeAtividade(String ctx, String nomeAtividade) throws Exception {
        return atividadeDao.buscarPorNomeAtividade(ctx, nomeAtividade);
    }

    public void syncAtividadesIA(final String ctx, final String ctxIA) throws Exception{
        //update atividade set idia = 'IA_' || LPAD(codigo::text, 6, '0')
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/"+ctxIA);
        String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
        HttpGet httpGet = new HttpGet(treinoUrl + "/prest/atividades/" + ctxIA + "/app/consultarTodasAtividades?crossfit=false");
        httpGet.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpGet);
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(responseBody);
        if (json.has("sucesso")) {
            List<AtividadeJSON> atividadesIA = JSONMapper.getList(json.getJSONArray("sucesso"), AtividadeJSON.class);
            for(AtividadeJSON atvIA : atividadesIA) {
                Atividade atividade = atividadeDao.obterPorNomeExato(ctx, atvIA.getNome());
                if(atividade == null){
                    atividade = new Atividade();
                    atividade.setNome(atvIA.getNome());
                    atividade.setNomeOriginalIA(atvIA.getNome());
                    atividade.setIdIA(atvIA.getIaID());
                    atividade.setUsarNaPrescricao(false);
                    atividade = atividadeDao.insert(ctx, atividade);
                    incluirLog(ctx, atividade.getCodigo().toString(), "", "",
                            atividade.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE ATIVIDADE",
                            EntidadeLogEnum.ATIVIDADE, "Atividade", "Sincronizador Treino IA", logDao, true);
                } else if(UteisValidacao.emptyString(atividade.getIdIA())){
                    Atividade atvAntesAlteracao = atividade.clone();
                    atvAntesAlteracao.setIdIA("");
                    atvAntesAlteracao.setNomeOriginalIA("");

                    atividade.setIdIA(atvIA.getIaID());
                    atividade.setUsarNaPrescricao(true);
                    atividade.setNomeOriginalIA(atvIA.getNome());
                    atividade = atividadeDao.update(ctx, atividade);
                    incluirLog(ctx, atividade.getCodigo().toString(), "",
                            atvAntesAlteracao.getDescricaoParaLog(atividade),
                            atividade.getDescricaoParaLog(atvAntesAlteracao),
                            "ALTERAÇÃO", "ALTERAÇÃO DE ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade",
                            "Sincronizador Treino IA", logDao, true);
                }
                if(UteisValidacao.emptyList(atividade.getAnimacoes())
                        &&  !UteisValidacao.emptyString(atvIA.getImg())){
                    addGif(ctx, atividade, atvIA);
                }
            }
            return;
        }
        throw new Exception("nao foi possivel obter as atividades IA");
    }

    public void storeAtividadesCache(final String ctx, final List<AtividadeJSON> atividades, boolean crossfit) {
        if (!crossfit)
            mapTodasAtividades.put(ctx, atividades);
        else
            mapTodasAtividadesCross.put(ctx, atividades);
    }

    public List<AtividadeJSON> getAtividadesCache(final String ctx, boolean crossfit) {
        if (!crossfit)
            return mapTodasAtividades.get(ctx);
        else
            return mapTodasAtividadesCross.get(ctx);
    }

    public List<Atividade> consultaAtividadesMontagem(final String ctx, Map<CadastrosAuxiliaresEnum, List<Integer>> params,
                                                      boolean comGrupo,
                                                      String nome,
                                                      List<Integer> ignorar,
                                                      Integer empresa, PaginadorDTO paginadorDTO) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" FROM Atividade obj ");
        if(comGrupo){
            sql.append(" left join AtividadeGrupoMuscular agm on agm.atividade_codigo = obj.codigo ");
            sql.append(" left join grupoMuscular gm on agm.grupoMuscular_codigo = gm.codigo ");
        }
        sql.append(" WHERE obj.ativo is true ");
        sql.append(" AND (obj.idIA is null or obj.idIA = '' OR obj.usarNaPrescricao is true) ");
        if(!UteisValidacao.emptyList(ignorar)){
            sql.append(" AND obj.codigo not in (");
            String str = "";
            for (Integer s : ignorar) {
                str += "," + s;
            }
            sql.append(str.replaceFirst(",", ""));
            sql.append(") ");
        }
        sql.append(" AND obj.crossfit is false ");
        sql.append(" AND TRIM(obj.nome) NOT LIKE '' ");
        if (empresa != null) {
            sql.append(" and (todasEmpresas is true or ");
            sql.append(" obj.codigo IN (SELECT atividade_codigo from AtividadeEmpresa atve ");
            sql.append(" inner join empresa em on em.codigo = atve.empresa_codigo ");
            sql.append(" WHERE em.codZW = ");
            sql.append(empresa).append("))");
        }
        for (CadastrosAuxiliaresEnum atr : params.keySet()) {
            if (params.get(atr) == null || params.get(atr).isEmpty()) {
                continue;
            }
            String codigos = "";
            for (Integer codigo : params.get(atr)) {
                codigos += "," + codigo;
            }
            sql.append(" AND obj.codigo IN (SELECT sub.atividade_codigo FROM ");
            sql.append(atr.getNomeEntidade());
            sql.append(" sub WHERE sub.").append(atr.getNomeAtributo()).append("_codigo IN (").append(codigos.replaceFirst(",", "")).append("))");
        }
        if (!UteisValidacao.emptyString(nome)) {
            sql.append(params.isEmpty() ? " WHERE " : " AND ");
            if (comGrupo) {
                sql.append(" (unaccent(lower(gm.nome)) LIKE unaccent('%").append(nome.toLowerCase()).append("%') OR ");
            }
            sql.append(" unaccent(lower(obj.nome)) LIKE unaccent('%").append(nome.toLowerCase()).append("%') ");
            sql.append(comGrupo ? " ) " : "");
        }
        if(paginadorDTO.getSize() == 0l){
            ResultSet rsCount = atividadeFichaDao.createStatement(ctx, "SELECT COUNT(obj.codigo) as total " + sql);
                paginadorDTO.setQuantidadeTotalElementos(new Long(rsCount.next() ? rsCount.getInt("total") : 0));
            paginadorDTO.setSize(paginadorDTO.getQuantidadeTotalElementos());
            sql.append(" ORDER BY obj.nome ");
        } else {
            paginadorDTO.setQuantidadeTotalElementos(paginadorDTO.getSize());
            sql.append(" ORDER BY obj.nome limit ").append(paginadorDTO.getSize());
        }

        List<Atividade> atividadesMontagem = new ArrayList<>();
         ResultSet rs = atividadeFichaDao.createStatement(ctx, "SELECT distinct obj.codigo, obj.nome, obj.tipo,  " +
                "(select coalesce(fotokey, '') || ';' || coalesce(fotokeypequena, '') || ';' || coalesce(fotokeyminiatura, '') from AtividadeAnimacao where atividade_codigo = obj.codigo limit 1) as imagem, " +
                "(select animacao_codigo from AtividadeAnimacao where atividade_codigo = obj.codigo and fotoKeyminiatura is null limit 1) as codigo_catalogo " + sql);
            while (rs.next()) {
                Atividade atividade = new Atividade();
                atividade.setCodigo(rs.getInt("codigo"));
                atividade.setNome(rs.getString("nome"));
                String[] imagem = isNotBlank(rs.getString("imagem")) ? rs.getString("imagem").split(";") : null;
                Integer catalogo = rs.getInt("codigo_catalogo");
                atividade.setTipoCod(rs.getInt("tipo"));
                atividade.setAnimacoes(new ArrayList() {{
                    if (imagem != null && imagem.length > 0) {
                        AtividadeAnimacao atividadeAnimacao = new AtividadeAnimacao();
                        try {
                            atividadeAnimacao.setFotoKey(imagem[0]);
                        } catch (Exception ignore) {
                        }
                        try {
                            atividadeAnimacao.setFotoKeyPequena(imagem[1]);
                        } catch (Exception ignore) {
                        }
                        try {
                            atividadeAnimacao.setFotoKeyMiniatura(imagem[2]);
                        } catch (Exception ignore) {
                        }
                        add(atividadeAnimacao);
                    } else if (catalogo > 0) {
                        Animacao animacao = animacaoDao.findById(ctx, catalogo);
                        AtividadeAnimacao atividadeAnimacao = new AtividadeAnimacao();
                        atividadeAnimacao.setAnimacao(animacao);
                        add(atividadeAnimacao);
                    }
                }});
                atividadesMontagem.add(atividade);
            }
        return atividadesMontagem;

    }

    private List<AtividadeJSON> listaAtividadesMatriz(String matriz) throws Exception{
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/"+matriz);
        String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");
        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/atividades/"+matriz+"/todas-full-relacionamentos");
        httpPost.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpPost);
        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(responseBody);
        if (json.has("return")) {
            return JSONMapper.getList(json.getJSONArray("return"), AtividadeJSON.class);
        }
        throw new Exception("nao foi possivel obter a lista de atividades");
    }

    public String replicarAtividade(String ctxMatriz, Integer codigoEmpresaZWMatriz, String ctxFilial, Integer codigoEmpresaZWFilial, Boolean status) throws ServiceException {
        try {
            List<AtividadeJSON> listaAtividadesMatriz = listaAtividadesMatriz(ctxMatriz);
            List<Atividade> listaAtividadesFilial = atividadeDao.listarAtividades(ctxFilial, null, null, null, false, true, new ArrayList<>(), codigoEmpresaZWFilial);

            List<Atividade> listaAtividadesFilialRecarregadas = listaAtividadesFilial.stream()
                    .map(atividade -> {
                        try {
                            Atividade atividadeRecarregada = recarregarAtividade(ctxFilial, atividade);
                            return atividadeRecarregada;
                        } catch (ServiceException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());


            // Função para normalizar os nomes
            Function<String, String> normalizarNome = nome -> {
                if (nome == null) return null;
                String nomeSemAcento = Normalizer.normalize(nome, Normalizer.Form.NFD)
                        .replaceAll("\\p{M}", "");
                String nomeNormalizado = nomeSemAcento.replaceAll("[^\\p{L}\\s]", "")
                        .replaceAll("\\s+", " ")
                        .toLowerCase()
                        .trim();
                return nomeNormalizado;
            };

            Map<String, List<Atividade>> mapaAtividadesFilial = listaAtividadesFilialRecarregadas.stream()
                    .collect(Collectors.groupingBy(
                            atividade -> normalizarNome.apply(atividade.getNome())
                    ));

            // Verificar e adicionar/atualizar atividades da matriz na filial com base no modelo
            for (AtividadeJSON atividadeMatriz : listaAtividadesMatriz) {
                if (atividadeMatriz == null || atividadeMatriz.getNome() == null) {
                    System.out.println("Atividade da matriz ou nome da atividade é nulo: " + atividadeMatriz);
                    continue;
                }

                String nomeAtividadeMatrizNormalized = normalizarNome.apply(atividadeMatriz.getNome()).trim();

                List<Atividade> atividadesFilial = mapaAtividadesFilial.getOrDefault(nomeAtividadeMatrizNormalized, Collections.emptyList());

                if (!atividadesFilial.isEmpty()) {
                    for (Atividade atividadeFilial : atividadesFilial) {
                        atividadeFilial.setNome(atividadeMatriz.getNome());
                        List<AtividadeAnimacao> animacoes = getAtividadeAnimacaoDao().obterImagens(ctxFilial, atividadeFilial.getCodigo());
                        if (animacoes == null) {
                            animacoes = new ArrayList<>();
                        }
                        atividadeFilial.setAnimacoes(animacoes);

                        if (!atividadeFilial.getAnimacoes().isEmpty()) {
                            List<AtividadeAnimacao> animacoesAtuais = new ArrayList<>(atividadeFilial.getAnimacoes());
                            for (AtividadeAnimacao animacaoAtual : animacoesAtuais) {
                                atividadeAnimacaoDao.delete(ctxFilial, animacaoAtual);
                            }
                        }
                        povoadorAtividadeService.salvarAtividadeAnimacao(ctxFilial, atividadeFilial, null, atividadeMatriz.getImg(), atividadeMatriz.getThumb(), atividadeMatriz.getImgMedium());
                    }
                } else {
                    povoadorAtividadeService.salvarAtividadeImportadaDaMatrizNaFilial(ctxFilial, atividadeMatriz);
                }
            }

            if (status) {
                for (Atividade atividadeFilial : listaAtividadesFilial) {
                    String nomeFilialNormalized = normalizarNome.apply(atividadeFilial.getNome());
                    if (!listaAtividadesMatriz.stream().anyMatch(atividadeMatriz ->
                            normalizarNome.apply(atividadeMatriz.getNome()).equals(nomeFilialNormalized))) {
                        atividadeFilial.setAtivo(true);
                        atividadeDao.update(ctxFilial, atividadeFilial);
                    }
                }
            }
            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode jsonResposta = objectMapper.createObjectNode();
            jsonResposta.put("sucesso", true);

            return objectMapper.writeValueAsString(jsonResposta);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_INCLUIR_ATIVIDADE, e);
        }
    }



    @Override
    public String processoReduzirTamanhoGif(double tamanhoMaximo, boolean somenteValidacao, Integer idAtividade) throws ServiceException {
        if (UteisValidacao.emptyNumber(tamanhoMaximo) && tamanhoMaximo <= 0) {
            return "ATENÇÃO: O tamanho máximo permitido não foi informado ou é igual a zero!";
        }
        Integer qtImgGrande = 0;
        Integer qtSucesso = 0;
        try {
            String ctx = sessaoService.getUsuarioAtual().getChave();
            String sql = "SELECT codigo, atividade_codigo, fotokey FROM atividadeanimacao a WHERE fotokey ILIKE '%.gif'";
            if (!UteisValidacao.emptyNumber(idAtividade)) {
                sql += " AND atividade_codigo = " + idAtividade;
            }
            try (ResultSet rs = atividadeDao.createStatement(ctx, sql.toString())) {
                while (rs.next()) {
                    System.out.println("Atv cod: " + rs.getInt("atividade_codigo") + " - AtvAni cod: " + rs.getInt("codigo") + " - fotokey: " + rs.getString("fotokey"));
                    if (rs.getString("fotokey") != null) {
                        byte[] imageBytes = MidiaService.getInstance().downloadObjectWithKeyByteArray(rs.getString("fotokey"));
                        System.out.println("---> O tamanho do gif é: " + imageBytes.length / (1024.0 * 1024.0));
                        if (imageBytes.length / (1024.0 * 1024.0) > tamanhoMaximo) {
                            qtImgGrande++;
                            if (!somenteValidacao) {
                                byte[] resizedImageBytes = resizeAnimatedGif(imageBytes, tamanhoMaximo);
                                System.out.println("---> O tamanho do gif redimensionado é: " + resizedImageBytes.length / (1024.0 * 1024.0));
                                String retorno = MidiaService.getInstanceWood().uploadObjectFromByteArrayV2(rs.getString("fotokey"), resizedImageBytes);
                                System.out.println("---> Retorno s3: " + retorno);
                                if (retorno.equalsIgnoreCase("ok")) {
                                    qtSucesso++;
                                }
                            }
                        }
                    }
                    System.out.println("");
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, AtividadeServiceImpl.class);
        }

        if (somenteValidacao) {
            return "SOMENTE_VALIDACAO: Existem " + qtImgGrande + " imagens de gif em atividades que excedem o tamanho de " + tamanhoMaximo + "MB.";
        }
        return "Existem " + qtImgGrande + " imagens de gif em atividades que excedem o tamanho de " + tamanhoMaximo + "MB e " + qtSucesso + " foram reduzidas e atualizadas com sucesso na S3.";
    }

    private static byte[] resizeAnimatedGif(byte[] imageBytes, double maxSizeMB) throws IOException {
        byte[] resizedImageBytes = null;
        double currentSizeMB = 0.0;

        double originalSizeMB = imageBytes.length / (1024.0 * 1024.0);
        if (originalSizeMB <= maxSizeMB) {
            return imageBytes;
        }

        do {
            ImageReader reader = ImageIO.getImageReadersByFormatName("gif").next();
            try (ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
                 ImageInputStream in = ImageIO.createImageInputStream(bis)) {
                reader.setInput(in);

                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                GifSequenceWriter writer = new GifSequenceWriter(ImageIO.createImageOutputStream(baos));
                int numFrames = reader.getNumImages(true);
                // Gif utiliza frames, então é necessário reduzir frame por frame
                for (int frameIndex = 0; frameIndex < numFrames; frameIndex++) {
                    BufferedImage frame = reader.read(frameIndex);

                    // Redimensiona o frame
                    int newWidth = (int) (frame.getWidth() / 1.2);
                    int newHeight = (int) (frame.getHeight() / 1.2);
                    BufferedImage resizedFrame = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
                    resizedFrame.getGraphics().drawImage(frame, 0, 0, newWidth, newHeight, null);

                    // Escreve o frame redimensionado
                    writer.writeToSequence(resizedFrame);
                }
                writer.close();
                resizedImageBytes = baos.toByteArray();
                currentSizeMB = resizedImageBytes.length / (1024.0 * 1024.0);

                if (currentSizeMB > maxSizeMB) {
                    imageBytes = resizedImageBytes;
                }
            }
        } while (currentSizeMB > maxSizeMB);

        return resizedImageBytes;
    }

    @Override
    public String excluirFotokeyAwsCsv(String csvBase64Data) throws ServiceException {
        int qtSucesso = 0;
        int qtError = 0;
        byte[] data = Base64.getDecoder().decode(csvBase64Data);
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(data), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                String[] columns = line.split(";");
                String valor = columns[0];
                if (!valor.equalsIgnoreCase("fotokey")) {
                    MidiaService.getInstanceWood().deleteObject(valor);
                    qtSucesso++;
                }
            }
        } catch (Exception ex) {
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            qtError++;
        }
        return qtSucesso + " fotokey removidas com sucesso e " + qtError + " excessões ocorreram durente o processo.";
    }

    public Atividade recarregarAtividade(String ctx, Atividade atividade) throws ServiceException {
        Atividade atv = new Atividade();
        atv.setAnimacoes(atividadeAnimacaoDao.obterImagens(ctx, atividade.getCodigo()));

        StringBuilder sqlAtvEmpresas = new StringBuilder();
        sqlAtvEmpresas.append("SELECT obj FROM AtividadeEmpresa obj WHERE obj.atividade.codigo = :atividadeId");
        HashMap<String, Object> param = new HashMap<>();
        param.put("atividadeId", atividade.getCodigo());
        atv.setEmpresasHabilitadas(getAtividadeEmpresaService().obterPorParam(ctx, sqlAtvEmpresas.toString(), param));

        atv.setNome(atividade.getNome());
        atv.setCodigo(atividade.getCodigo());
        atv.setDescricao(atividade.getDescricao());
        atv.setAtivo(atividade.isAtivo());
        atv.setSeriesApenasDuracao(atividade.getSeriesApenasDuracao());
        atv.setTipo(atividade.getTipo());
        atv.setSelecionado(atividade.getSelecionado());
        atv.setAjustes(atividade.getAjustes());
        atv.setAdicionado(atividade.getAdicionado());
        atv.setTodasEmpresas(atividade.getTodasEmpresas());
        atv.setVersao(atividade.getVersao());
        atv.setCrossfit(atividade.getCrossfit());
        atv.setLinkVideo(atividade.getLinkVideo());
        atv.setUnidadeMedida(atividade.getUnidadeMedida());
        atv.setCategoriaAtividadeWod(atividade.getCategoriaAtividadeWod());

        return atv;
    }

    public String replicarImagensAtividades(String ctxMatriz, Integer codigoEmpresaZWMatriz, String ctxFilial,
                                            Integer codigoEmpresaZWFilial, Boolean substituirImagens) throws ServiceException {
        try {
            List<AtividadeJSON> listaAtividadesMatriz = listaAtividadesMatriz(ctxMatriz);
            List<Atividade> listaAtividadesFilial = atividadeDao.listarAtividades(ctxFilial, null, null, null, false, true, new ArrayList<>(), codigoEmpresaZWFilial);
            List<Atividade> listaAtividadesFilialRecarregadas = listaAtividadesFilial.stream()
                    .map(atividade -> {
                        try {
                            return recarregarAtividade(ctxFilial, atividade);
                        } catch (ServiceException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .collect(Collectors.toList());

            Function<String, String> normalizarNome = nome -> {
                if (nome == null) return null;
                String nomeSemAcento = Normalizer.normalize(nome, Normalizer.Form.NFD)
                        .replaceAll("\\p{M}", "");
                String nomeNormalizado = nomeSemAcento.replaceAll("[^\\p{L}\\s]", "")
                        .replaceAll("\\s+", " ")
                        .toLowerCase()
                        .trim();
                return nomeNormalizado;
            };

            Map<String, List<Atividade>> mapaAtividadesFilial = listaAtividadesFilialRecarregadas.stream()
                    .collect(Collectors.groupingBy(atividade -> normalizarNome.apply(atividade.getNome())));

            for (AtividadeJSON atividadeMatriz : listaAtividadesMatriz) {
                if (atividadeMatriz == null || atividadeMatriz.getNome() == null) {
                    System.out.println("Atividade da matriz ou nome nulo: " + atividadeMatriz);
                    continue;
                }

                String nomeNormalizadoMatriz = normalizarNome.apply(atividadeMatriz.getNome()).trim();
                List<Atividade> atividadesFilial = mapaAtividadesFilial.getOrDefault(nomeNormalizadoMatriz, Collections.emptyList());

                for (Atividade atividadeFilial : atividadesFilial) {
                    List<AtividadeAnimacao> animacoes = getAtividadeAnimacaoDao().obterImagens(ctxFilial, atividadeFilial.getCodigo());
                    if (animacoes == null) {
                        animacoes = new ArrayList<>();
                    }
                    atividadeFilial.setAnimacoes(animacoes);

                    if (!atividadeFilial.getAnimacoes().isEmpty()) {
                        if (substituirImagens) {
                            List<AtividadeAnimacao> animacoesAtuais = new ArrayList<>(atividadeFilial.getAnimacoes());
                            for (AtividadeAnimacao animacaoAtual : animacoesAtuais) {
                                atividadeAnimacaoDao.delete(ctxFilial, animacaoAtual);
                            }
                            povoadorAtividadeService.salvarAtividadeAnimacao(ctxFilial, atividadeFilial, null,
                                    atividadeMatriz.getImg(), atividadeMatriz.getThumb(), atividadeMatriz.getImgMedium());
                        }
                    } else {
                        povoadorAtividadeService.salvarAtividadeAnimacao(ctxFilial, atividadeFilial, null,
                                atividadeMatriz.getImg(), atividadeMatriz.getThumb(), atividadeMatriz.getImgMedium());
                    }
                }
            }

            ObjectMapper objectMapper = new ObjectMapper();
            ObjectNode jsonResposta = objectMapper.createObjectNode();
            jsonResposta.put("sucesso", true);
            return objectMapper.writeValueAsString(jsonResposta);
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_INCLUIR_ATIVIDADE, e);
        }
    }

    public String desativarAtividadesComIdiaPreenchido() throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        int qtAtualizadas = 0;

        try {
            String sql = "SELECT codigo, nome FROM atividade " +
                    "WHERE idia IS NOT NULL OR idia2 IS NOT NULL AND ativo = true";

            try (ResultSet rs = atividadeDao.createStatement(ctx, sql)) {
                while (rs.next()) {
                    Integer codigo = rs.getInt("codigo");

                    Atividade atividade = atividadeDao.obterPorId(ctx, codigo);
                    Atividade atvAntesAlteracao = atividade.clone();

                    if (atividade != null) {
                        atividade.setAtivo(false);
                        atividade = atividadeDao.update(ctx, atividade);

                        incluirLog(ctx, atividade.getCodigo().toString(), "",
                                atvAntesAlteracao.getDescricaoParaLog(atividade),
                                atividade.getDescricaoParaLog(atvAntesAlteracao),
                                "ALTERAÇÃO", "INATIVAÇÃO POR IDIA/IDIA2", EntidadeLogEnum.ATIVIDADE, "Atividade",
                                "Script automático", logDao, true);

                        qtAtualizadas++;
                    }
                }
            }
            return qtAtualizadas + " atividades com idia e/ou idia2 preenchidos foram desativadas com sucesso.";
        } catch (Exception e) {
            Uteis.logar(e, AtividadeServiceImpl.class);
            throw new ServiceException("Erro ao desativar atividades com IDIA/IDIA2 preenchidos", e);
        }
    }

    @Override
    public Atividade importarAtividadeIa(String ctx, String atividadeIdIA) {
        Atividade atividade = new Atividade();
        try {
            ConfiguracaoSistema valorAntesConfigPermitirCriarTreinoIa = configuracaoSistemaService.consultarPorTipo(ctx, ConfiguracoesEnum.PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA);
            boolean configAntesPermitirCriarTreinoIa = valorAntesConfigPermitirCriarTreinoIa != null && Boolean.parseBoolean(valorAntesConfigPermitirCriarTreinoIa.getValor());

            if (!configAntesPermitirCriarTreinoIa) {
                throw new ServiceException("A configuração 'Permitir criação de treino automatizado (I.A)' está desabilitada. Por isso, esta ação foi interrompida!");
            }

            String exportUrl = Aplicacao.getProp(Aplicacao.urlTreinoIa) + "/export-exercises/" + atividadeIdIA;
            CloseableHttpClient clientExport = HttpClients.createDefault();
            HttpGet getExport = new HttpGet(exportUrl);
            getExport.setHeader("Content-Type", "application/json");
            getExport.setHeader("access-token", Aplicacao.getProp(Aplicacao.accessTokenTreinoIa));
            getExport.setHeader("zw-key", ctx);

            HttpResponse responseExport = clientExport.execute(getExport);
            String responseExportString = EntityUtils.toString(responseExport.getEntity(), StandardCharsets.UTF_8);
            int statusExport = responseExport.getStatusLine().getStatusCode();
            if (statusExport != HttpStatus.SC_OK) {
                throw new RuntimeException("Erro ao exportar atividades: HTTP " + statusExport);
            }

            JSONObject atividadeJson = new JSONObject(responseExportString);
            String idIA = atividadeJson.getString("exercise_id");
            String nomeAtividadeIa = atividadeJson.getString("exercise_name");
            JSONArray instructions = atividadeJson.getJSONArray("instructions");
            String descricaoAtividade = "";
            for (int j = 0; j < instructions.length(); j++) {
                descricaoAtividade += instructions.getString(j) + " \n";
            }
            String tipoAtividade = atividadeJson.getString("system_targeted");
            String urlImg = atividadeJson.getString("gif_url");
            atividade = obterPorIdIAVersaoDois(ctx, Integer.parseInt(idIA));
            if (atividade == null || UteisValidacao.emptyNumber(atividade.getCodigo())) {
                Atividade novaAtividade = new Atividade();
                Atividade atividadePorNome = buscarPorNomeAtividade(ctx, nomeAtividadeIa);
                if (atividadePorNome != null && atividadePorNome.getCodigo() != null) {
                    // alternativa para não conflitar devido nome de atividade ser único e não poder sobrescrever uma atividade do banco por uma da ia
                    novaAtividade.setNome(" " + nomeAtividadeIa);
                } else {
                    novaAtividade.setNome(nomeAtividadeIa);
                }
                novaAtividade.setDescricao(descricaoAtividade);
                novaAtividade.setNomeOriginalIA(nomeAtividadeIa);
                novaAtividade.setTipo(TipoAtividadeEnum.getFromDescricao(tipoAtividade));
                novaAtividade.setVersao(0);
                novaAtividade.setIdIA2(Integer.parseInt(idIA));
                atividade = inserir(ctx, novaAtividade);

                incluirLog(ctx, atividade.getCodigo().toString(), "", "",
                        atividade.getDescricaoParaLog(null), "INCLUSÃO", "INCLUSÃO DE ATIVIDADE",
                        EntidadeLogEnum.ATIVIDADE, "Atividade", "Sincronizador Treino IA", logDao, true);
            } else {
                Atividade atvAntesAlteracao = atividade.clone();
                atividade.setNome(nomeAtividadeIa);
                atividade.setNomeOriginalIA(nomeAtividadeIa);
                atividade.setDescricao(descricaoAtividade);
                atividade.setTipo(TipoAtividadeEnum.getFromDescricao(tipoAtividade));
                atividade.setVersao(atividade.getVersao() + 1);
                alterar(ctx, atividade);

                incluirLog(ctx, atividade.getCodigo().toString(), "",
                        atvAntesAlteracao.getDescricaoParaLog(atividade),
                        atividade.getDescricaoParaLog(atvAntesAlteracao),
                        "ALTERAÇÃO", "ALTERAÇÃO DE ATIVIDADE", EntidadeLogEnum.ATIVIDADE, "Atividade",
                        "Sincronizador Treino IA", logDao, true);
            }
            if (!UteisValidacao.emptyString(urlImg) && atividade != null && !UteisValidacao.emptyNumber(atividade.getCodigo())) {
                // para limpar qualquer fotokey incorreto já presente em produção
                atividadeAnimacaoDao.deleteFotoKeyAtividade(ctx, atividade.getCodigo());
                byte[] imagemBase64 = converteImagemParaBytes(urlImg);
                List<AtividadeImagemUploadTO> atividadeImagemUploadTOList = new ArrayList<>();
                AtividadeImagemUploadTO atividadeImagemUploadTO = new AtividadeImagemUploadTO();
                atividadeImagemUploadTO.setData(imagemBase64);
                atividadeImagemUploadTO.setNome("imagem-ia-" + atividade.getCodigo() + ".gif");
                atividadeImagemUploadTOList.add(atividadeImagemUploadTO);
                salvarMidiaNuvemEndpoint(ctx, atividadeImagemUploadTOList, atividade);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Uteis.logarDebug("[AtividadeServiceImpl.importarAtividadeIa] Erro ao importar uma atividade da IA, erro: " + e.getMessage());
        }
        return atividade;
    }

    private static byte[] converteImagemParaBytes(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            try (InputStream is = url.openStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    baos.write(buffer, 0, bytesRead);
                }
            }
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public RelatorioExclusaoAtividadeIADTO removerAtividadesGeradasPorIA(Boolean forcarExclusaoTotal) throws ServiceException {
        String ctx = sessaoService.getUsuarioAtual().getChave();
        RelatorioExclusaoAtividadeIADTO relatorio = new RelatorioExclusaoAtividadeIADTO();

        try {
            List<Atividade> atividadesIA = atividadeDao.listarAtividadesComIdia2NaoNulo(ctx);
            relatorio.setTotalAtividadesAnalisadas(atividadesIA.size());

            if (atividadesIA.isEmpty()) {
                relatorio.setMensagem("Nenhuma atividade gerada por IA (com idia2 preenchido) foi encontrada para análise.");
                return relatorio;
            }

            List<Atividade> atividadesParaExcluirSemUso = new ArrayList<>();
            List<Atividade> atividadesParaExcluirComUso = new ArrayList<>(); // Lista para o modo forçado
            List<AtividadeNaoExcluidaInfoDTO> atividadesNaoExcluidasInfos = new ArrayList<>();

            for (Atividade atividade : atividadesIA) {
                List<Integer> fichasEmUso = atividadeFichaDao.findFichaCodigosByAtividadeCodigo(ctx, atividade.getCodigo());

                if (fichasEmUso.isEmpty()) {
                    // Atividade sem uso, sempre segura para excluir.
                    atividadesParaExcluirSemUso.add(atividade);
                } else {
                    if (forcarExclusaoTotal) {
                        // Modo forçado: atividade em uso será excluída.
                        atividadesParaExcluirComUso.add(atividade);
                    } else {
                        // Modo seguro (padrão): atividade em uso é mantida e reportada.
                        atividadesNaoExcluidasInfos.add(
                                new AtividadeNaoExcluidaInfoDTO(atividade.getCodigo(), atividade.getNome(), fichasEmUso)
                        );
                    }
                }
            }

            // --- LÓGICA DE EXCLUSÃO ---

            // 1. Se o modo forçado estiver ativo, remove primeiro as referências em AtividadeFicha.
            if (forcarExclusaoTotal && !atividadesParaExcluirComUso.isEmpty()) {
                List<Integer> codigosComUsoParaExcluir = atividadesParaExcluirComUso.stream()
                        .map(Atividade::getCodigo)
                        .collect(Collectors.toList());
                atividadeFichaDao.deleteByAtividadeCodigos(ctx, codigosComUsoParaExcluir);
            }

            // 2. Junta todos os códigos de atividades que devem ser removidos.
            List<Atividade> todasAtividadesParaExcluir = new ArrayList<>();
            todasAtividadesParaExcluir.addAll(atividadesParaExcluirSemUso);
            todasAtividadesParaExcluir.addAll(atividadesParaExcluirComUso);

            if (!todasAtividadesParaExcluir.isEmpty()) {
                List<Integer> todosOsCodigosParaExcluir = todasAtividadesParaExcluir.stream()
                        .map(Atividade::getCodigo)
                        .collect(Collectors.toList());

                // 3. Exclui os relacionamentos (grupos, animações, etc.)
                atividadeDao.excluirRelacionamentosPorCodigos(ctx, todosOsCodigosParaExcluir);

                // 4. Exclui as atividades da tabela principal
                atividadeDao.excluirAtividadesPorCodigos(ctx, todosOsCodigosParaExcluir);
            }

            // --- MONTAGEM DO RELATÓRIO FINAL ---
            relatorio.setAtividadesNaoExcluidas(atividadesNaoExcluidasInfos);
            relatorio.setTotalAtividadesExcluidas(todasAtividadesParaExcluir.size());
            relatorio.setTotalAtividadesNaoExcluidas(atividadesNaoExcluidasInfos.size());

            String modo = forcarExclusaoTotal ? "MODO DE EXCLUSÃO TOTAL ATIVADO. " : "";
            String msg = String.format(
                    "%sProcesso finalizado. %d atividades analisadas. %d foram excluídas com sucesso. %d não puderam ser excluídas (modo seguro).",
                    modo,
                    relatorio.getTotalAtividadesAnalisadas(),
                    relatorio.getTotalAtividadesExcluidas(),
                    relatorio.getTotalAtividadesNaoExcluidas()
            );
            relatorio.setMensagem(msg);

            return relatorio;

        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_EXCLUIR_ATIVIDADE, e);
        }
    }

    @Override
    public String replicarAtividadesSubstituindo(String ctxOrigem, Integer codigoEmpresaZWOrigem, String ctxDestino, Integer codigoEmpresaZWDestino) throws ServiceException {
        try {
            List<AtividadeJSON> listaAtividadesOrigem = listaAtividadesMatriz(ctxOrigem);
            List<Atividade> listaAtividadesDestino = atividadeDao.listarAtividades(ctxDestino, null, null, null, false, true, new ArrayList<>(), codigoEmpresaZWDestino);

            List<AtividadeJSON> atividadesOrigemSemIA = listaAtividadesOrigem.stream()
                    .filter(atividade -> atividade.getIaID2() == null || UteisValidacao.emptyNumber(atividade.getIaID2()))
                    .collect(Collectors.toList());

            int atividadesInativadas = 0;
            for (Atividade atividadeDestino : listaAtividadesDestino) {
                if (atividadeDestino.isAtivo()) {
                    atividadeDestino.setAtivo(false);
                    atividadeDao.update(ctxDestino, atividadeDestino);
                    atividadesInativadas++;
                }
            }

            int atividadesReplicadas = 0;
            for (AtividadeJSON atividadeOrigem : atividadesOrigemSemIA) {
                povoadorAtividadeService.salvarAtividadeImportadaDaMatrizNaFilial(ctxDestino, atividadeOrigem);
                atividadesReplicadas++;
            }

            return String.format("Processo finalizado. %d atividades encontradas na origem (excluindo IA). %d atividades inativadas no destino. %d atividades replicadas com sucesso.",
                    atividadesOrigemSemIA.size(), atividadesInativadas, atividadesReplicadas);

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            throw new ServiceException(AtividadeExcecoes.ERRO_INCLUIR_ATIVIDADE, e);
        }
    }

}
