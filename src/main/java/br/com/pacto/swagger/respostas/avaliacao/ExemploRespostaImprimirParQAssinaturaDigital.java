package br.com.pacto.swagger.respostas.avaliacao;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Resposta da geração de PDF do questionário PAR-Q com assinatura digital")
public class ExemploRespostaImprimirParQAssinaturaDigital {

    @ApiModelProperty(value = "URL do arquivo PDF gerado contendo o questionário PAR-Q preenchido pelo aluno. " +
            "Esta URL permite o download direto do documento PDF que inclui todas as perguntas, respostas e " +
            "informações do questionário de prontidão para atividade física, incluindo a assinatura digital quando disponível.",
            example = "https://relatorio-ms.treino.com.br/temp/parq_aluno_ALU001_20240724_143022.pdf")
    private String RETURN;

    @ApiModelProperty(value = "URL da assinatura digital do aluno no questionário PAR-Q. " +
            "Contém o link para a imagem da assinatura eletrônica capturada durante o preenchimento do questionário. " +
            "Pode ser uma string vazia se o aluno não assinou digitalmente o questionário.",
            example = "https://storage.treino.com.br/assinaturas/parq_aluno_ALU001_assinatura_20240724.png")
    private String assinatura;

    @ApiModelProperty(value = "Mensagem de erro caso ocorra algum problema durante a geração do PDF. " +
            "Este campo só estará presente em caso de falha na operação e conterá detalhes sobre o erro ocorrido.",
            example = "Erro ao gerar PDF: Aluno não encontrado")
    private String erro;

    // Getters e Setters
    public String getRETURN() {
        return RETURN;
    }

    public void setRETURN(String RETURN) {
        this.RETURN = RETURN;
    }

    public String getAssinatura() {
        return assinatura;
    }

    public void setAssinatura(String assinatura) {
        this.assinatura = assinatura;
    }

    public String getErro() {
        return erro;
    }

    public void setErro(String erro) {
        this.erro = erro;
    }
}
